# Hammer (Loyalty Part) - API Documentation Plan

## APP Controllers

### 1. Subscriber Controller

**Base Path:** `/subscriber`

#### Endpoints:

- **GET** `/profile`
  - **Description:** Get user profile information
  - **Response:** User profile data

---

### 2. Loyalty Controller

**Base Path:** `/loyalty`

#### Card Management:

- **GET** `/getAllLoyaltyCards`

  - **Description:** Return all loyalty cards and card information for the user
  - **Response:** List of all user's loyalty cards with basic information

- **PUT** `/deactivateCard`

  - **Description:** Deactivate Card
  - **Parameters:**
    - `cardId` (optional) - Card ID
  - **Response:** -

- **PUT** `/activateCard`

  - **Description:** Activate Card
  - **Parameters:**
    - `cardId` (optional) - Card ID
  - **Response:** -

- **GET** `/getCardDetail`

  - **Description:** Return detailed card information (including points, card number, etc.)
  - **Parameters:**
    - `merchantId` (required) - Specific merchant's card details
  - **Response:** Card details with points balance and card number

- **POST** `/requestForLoyaltyCard`
  - **Description:** User request for a new loyalty card
  - **Parameters:**
    - `merchantId` (required) - Target merchant for the card
  - **Response:** New card information or request status

#### Points Management:

- **GET** `/pointsHistory`

  - **Description:** Check points transaction history
  - **Parameters:**
    - `merchantId` (optional) - Filter by specific merchant, if not provided returns all merchants
  - **Response:** Points transaction history

- **GET** `/getPointsByMerchant`

  - **Description:** Get user's points balance for a specific merchant
  - **Parameters:**
    - `merchantId` (required) - Target merchant ID
  - **Response:** Points balance for the specified merchant

- **POST** `/getPoints`
  - **Description:** Aet all points from each merchant
  - **Parameters:**
    - `name` (required) - Merchant Name
  - **Response:** List of point and merchant

---

### 3. Voucher Controller

#### Voucher Management:

- **GET** `/`

  - **Description:** Check all vouchers
  - **Parameters:**
    - `merchantId` (optional) - Filter by specific merchant
    - `status` (optional) - Filter by voucher status (redeemed, used, expired)
  - **Response:** All vouchers

- **GET** `/getActiveVouchers`

  - **Description:** Check all Active vouchers
  - **Response:** All active vouchers

- **GET** `/getVoucherDetails`

  - **Description:** Check voucher details
  - **Parameters:**
    - `voucherId` (optional) - Filter by specific merchant
  - **Response:** voucher detail

- **GET** `/getAvailableVouchers`

  - **Description:** Check all available vouchers that can be redeemed
  - **Parameters:**
    - `merchantId` (required) - Target merchant's vouchers
  - **Response:** List of available vouchers with required points

- **POST** `/redeemVoucher`

  - **Description:** Redeem voucher using points
  - **Parameters:**
    - `merchantId` (required) - Merchant ID
    - `voucherId` (required) - Voucher ID to redeem
  - **Response:** Redemption status and updated points balance

- **GET** `/voucherRedemptionQr`
  - **Description:** Return QR code data for merchant to scan
  - **Parameters:**
    - `voucherId` (required) - Redeemed voucher ID
  - **Response:** QR code text/data for mobile to generate QR code

---

### 4. Merchant Controller

**Base Path:** `/merchant`

#### Merchant Info:

- **GET** `/getAllMerchants`

  - **Description:** Get list of all available merchants
  - **Response:** List of merchants with basic information

- **GET** `/getMerchantInfo`
  - **Description:** Get detailed information about a specific merchant
  - **Parameters:**
    - `merchantId` (required) - Target merchant ID
  - **Response:** Merchant details including available services

---

## Admin Portal Controllers

### 1. Voucher Management

**Base Path:** `/voucher-management`

#### Endpoints:

**GET** `/getAllVouchers`

- **Description:** Get all vouchers
- **Query Parameters:**
  - `status` (optional) – Filter by voucher status (e.g., `active`, `expired`, `inactive`)
  - `name` (optional) – Filter by voucher name
- **Response:**  
  Returns a list of all matching vouchers

**GET** `/getVoucherById/{id}`

- **Description:** Get voucher by ID
- **Path Parameters:**
  - `id` – Voucher ID
- **Response:**  
  Returns the details of the specified voucher

**POST** `/createVoucher`

- **Description:** Create a new voucher
- **Request Body:**
  - `name` – Voucher name
  - `amount` – Voucher amount
  - `expiryDate` – Expiration date
  - `description` – (optional) Voucher description
- **Response:**  
  Returns the created voucher details

**PUT** `/updateVoucher/{id}`

- **Description:** Update an existing voucher
- **Request Body:**
  - Any updatable fields (e.g., `name`, `amount`, `expiryDate`, etc.)
- **Response:**  
  Returns the updated voucher details

**PUT** `/deactivateVoucher/{id}`

- **Description:** Deactivate a voucher
- **Path Parameters:**
  - `id` – Voucher ID
- **Response:**  
  Confirmation of deactivation

**PUT** `/activateVoucher/{id}`

- **Description:** Activate a voucher
- **Path Parameters:**
  - `id` – Voucher ID
- **Response:**  
  Confirmation of activation

---

## SDK Controller

### 1. Transaction

**Base Path:** `/client-sdk`

#### Endpoints:

**POST** `/initTxn`

- **Description:** Initialize transaction with NFC loyalty card and voucher
- **Request Body:**
  - `cardValue` – NFC loyalty card value
  - `txnAmount` – Transaction amount
  - `voucherId` (optional) – Voucher ID to apply
- **Response:**
  Returns transaction initialization details including:
  - `txnId` – Transaction ID
  - `amountToMake` – Final amount to be paid
  - `originalAmount` – Original transaction amount
  - `voucherAmount` – Voucher discount amount (if applicable)
  - `voucherName` – Applied voucher name (if applicable)

**POST** `/confirmTxn`

- **Description:** Confirm transaction completion
- **Request Body:**
  - txn id and txn details
- **Response:**
  Returns transaction confirmation status

**POST** `/validateCard`

- **Description:** Validate NFC loyalty card
- **Request Body:**
  - `cardValue` – NFC loyalty card value
- **Response:**
  Returns card validation details including:
  - `cardStatus` – Card status (e.g., `active`, `inactive`, `expired`)
  - `points` – Current loyalty points balance

## Admin App Controller

### 1. Assign Card

**Base Path:** `/admin`

#### Endpoints:

/getRequestId
/registerCard
/checkCard

1. loyalty card
2. loyalty transaction
3. loyalty points
4. loyalty card request
5. voucher
6. voucher redemptions
7. merchant
8. user

# Database Schema

#### LoyaltyCard

-id
-userNo
-cardId
-status 'REQUESTED' | 'ASSIGNED' | 'ACTIVE' | 'SUSPENDED' | 'DEACTIVATED'
-metadata json format

Indexes:
cardId unique
userNo (for lookup of user's card)

#### LoyaltyCardRequest

-id
-userNo
-status 'PENDING'|'APPROVED'|'REJECTED'
-reason optional
-assignedBy
-assignedAt

Indexes:
userId, requestStatus

#### LoyaltyPoint

-id
-userId
-merchantId
-pointsBalance
-pointsSpent
-pointsEarned

compound { userId: 1, merchantId: 1 } unique

#### LoyaltyTransaction

-id
-userId
-merchantId
-loyaltyCardId
-type 'EARN' | 'REDEEM' | 'ADJUST' | 'REFUND'
-points
-amount
-pointsBefore
-pointsAfter
-voucherId
-voucherRedemptionId
-n2tapTxnId
-metadata

#### Voucher

-id
-merchantId
-title
-description
-type 'DISCOUNT_PERCENT' | 'DISCOUNT_AMOUNT' | 'FIXED_AMOUNT'
-discountPercent
-discountAmount
-minSpend
-startDateTime
-endDateTime
-inventoryTotal
-inventoryRemaining
-perUserLimit
-status: 'ACTIVE'|'INACTIVE'|'EXPIRED'

merchantId, expireAt, status,

#### VoucherRedemption

- id
- voucherId
- userId
- merchantId
- pointsSpent
- discountGiven
- code
- status ('RESERVED' | 'REDEEMED' | 'CANCELLED' | 'EXPIRED')
- reservedAt
- redeemedAt
- expiresAt
- metadata
- txnId
