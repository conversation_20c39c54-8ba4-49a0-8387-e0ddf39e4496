const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * Read Clockify API Key from file
 */
function readClockifyApiKey() {
    try {
        const apiKeyPath = path.join(__dirname, 'clockify-apikey.txt');
        const apiKey = fs.readFileSync(apiKeyPath, 'utf8').trim();
        if (!apiKey) {
            throw new Error('Clockify API Key is empty');
        }
        return apiKey;
    } catch (error) {
        console.error('Failed to read Clockify API Key:', error.message);
        process.exit(1);
    }
}

/**
 * Send HTTPS request
 */
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = data ? JSON.parse(data) : {};
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error('Request failed: ' + error.message));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

/**
 * Load time entries to import
 */
function loadTimeEntries() {
    try {
        const filePath = path.join(__dirname, 'clockify-entries-to-import.json');
        const timeEntries = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        return timeEntries;
    } catch (error) {
        console.error('Failed to load time entries file:', error.message);
        console.error('Please run generate-clockify-entries.js first');
        process.exit(1);
    }
}

/**
 * Create a single time entry in Clockify
 */
async function createTimeEntry(apiKey, entry) {
    const requestBody = {
        start: entry.start,
        end: entry.end,
        projectId: entry.projectId,
        description: entry.description,
        billable: false
    };
    
    const postData = JSON.stringify(requestBody);
    
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: `/api/v1/workspaces/${entry.workspaceId}/time-entries`,
        method: 'POST',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    try {
        const response = await makeRequest(options, postData);
        
        if (response.statusCode === 201) {
            return { success: true, data: response.data };
        } else {
            return { 
                success: false, 
                error: `HTTP ${response.statusCode}: ${JSON.stringify(response.data)}` 
            };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * Add delay between requests to avoid rate limiting
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Import all time entries to Clockify
 */
async function importTimeEntries(apiKey, timeEntries) {
    console.log(`Starting import of ${timeEntries.length} time entries...`);
    console.log('='.repeat(60));
    
    const results = {
        success: 0,
        failed: 0,
        errors: []
    };
    
    for (let i = 0; i < timeEntries.length; i++) {
        const entry = timeEntries[i];
        const progress = `${i + 1}/${timeEntries.length}`;
        
        console.log(`[${progress}] Importing ${entry.date} - ${entry.projectName}...`);
        
        const result = await createTimeEntry(apiKey, entry);
        
        if (result.success) {
            results.success++;
            console.log(`  ✅ Success`);
        } else {
            results.failed++;
            results.errors.push({
                date: entry.date,
                project: entry.projectName,
                error: result.error
            });
            console.log(`  ❌ Failed: ${result.error}`);
        }
        
        // Add delay to avoid rate limiting (Clockify allows ~300 requests per minute)
        if (i < timeEntries.length - 1) {
            await delay(250); // 250ms delay = ~240 requests per minute
        }
    }
    
    return results;
}

/**
 * Save import results to file
 */
function saveImportResults(results, timeEntries) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: timeEntries.length,
            successful: results.success,
            failed: results.failed,
            successRate: `${((results.success / timeEntries.length) * 100).toFixed(1)}%`
        },
        errors: results.errors
    };
    
    const reportPath = path.join(__dirname, 'clockify-import-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\nImport report saved to: ${reportPath}`);
    
    return report;
}

/**
 * Main function
 */
async function main() {
    console.log('Clockify Time Entry Importer');
    console.log('='.repeat(50));
    
    try {
        const apiKey = readClockifyApiKey();
        const timeEntries = loadTimeEntries();
        
        console.log(`Loaded ${timeEntries.length} time entries to import`);
        
        if (timeEntries.length === 0) {
            console.log('No time entries to import. Exiting.');
            return;
        }
        
        // Show preview of first few entries
        console.log('\nPreview of entries to import:');
        console.log('-'.repeat(40));
        timeEntries.slice(0, 3).forEach((entry, index) => {
            console.log(`${index + 1}. ${entry.date} - ${entry.projectName}`);
            console.log(`   ${entry.description}`);
            console.log(`   ${entry.start} to ${entry.end}`);
        });
        
        if (timeEntries.length > 3) {
            console.log(`   ... and ${timeEntries.length - 3} more entries`);
        }
        
        // Confirm before proceeding
        console.log('\n⚠️  This will create time entries in Clockify.');
        console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...');
        
        await delay(5000);
        
        // Start import
        const results = await importTimeEntries(apiKey, timeEntries);
        
        // Show results
        console.log('\n' + '='.repeat(60));
        console.log('IMPORT COMPLETED');
        console.log('='.repeat(60));
        console.log(`✅ Successful: ${results.success}`);
        console.log(`❌ Failed: ${results.failed}`);
        console.log(`📊 Success Rate: ${((results.success / timeEntries.length) * 100).toFixed(1)}%`);
        
        if (results.errors.length > 0) {
            console.log('\nErrors:');
            results.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.date} (${error.project}): ${error.error}`);
            });
        }
        
        // Save report
        const report = saveImportResults(results, timeEntries);
        
        console.log('\n🎉 Import process completed!');
        
    } catch (error) {
        console.error('Import failed:', error.message);
        process.exit(1);
    }
}

// Run script
if (require.main === module) {
    main();
}

module.exports = {
    main,
    createTimeEntry,
    importTimeEntries
};
