[{"id": 21690, "project_id": 305, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-21T03:32:13.988Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 8, "action": "pushed", "ref_type": "branch", "commit_from": "69c53804aaa1a3b35691ce888fe70ad0717b13d7", "commit_to": "38896ed53ec709d547fb95e35ea7c315e695be30", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21689, "project_id": 305, "action_name": "accepted", "target_id": 1098, "target_iid": 9, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster CR0064, CR0075, CR0076", "created_at": "2025-07-21T03:32:13.461Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 21688, "project_id": 305, "action_name": "approved", "target_id": 1098, "target_iid": 9, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster CR0064, CR0075, CR0076", "created_at": "2025-07-21T03:32:10.690Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 21605, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:37:58.944Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 92, "action": "pushed", "ref_type": "branch", "commit_from": "761f0fd5ec6ae91cb2d54ea5e4b201d48ef50ff2", "commit_to": "bb72b19d55bd8102af234aec660720993285ab91", "ref": "master", "commit_title": "bump to 3.2.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21604, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:37:11.433Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "761f0fd5ec6ae91cb2d54ea5e4b201d48ef50ff2", "ref": "master-backup", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21602, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:36:36.750Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "tag", "commit_from": null, "commit_to": "2e864b5df25744204ce3b232aa009d4e198109d1", "ref": "v3.2.0-release", "commit_title": "bump to 3.2.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21601, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:36:04.859Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 92, "action": "pushed", "ref_type": "branch", "commit_from": "761f0fd5ec6ae91cb2d54ea5e4b201d48ef50ff2", "commit_to": "bb72b19d55bd8102af234aec660720993285ab91", "ref": "premaster-al<PERSON>", "commit_title": "bump to 3.2.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21600, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:27:02.141Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "761f0fd5ec6ae91cb2d54ea5e4b201d48ef50ff2", "ref": "premaster-al<PERSON>", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21599, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:25:42.287Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e1ebdaafb4d9bcbdd798fbe83158a44f369015c5", "commit_to": "ec1216631362cea4e0eeda5c1a2964c9fff827a4", "ref": "premaster", "commit_title": "Revert \"merge uat into premaster\"", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21594, "project_id": 465, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:00:20.713Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "2d17e48d7d06df2c332a069c3d08f3af9aac7fa8", "ref": "main", "commit_title": "Initial commit", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21593, "project_id": 465, "action_name": "created", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-17T02:00:15.348Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 21589, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-16T08:31:13.365Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 91, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "7d43d33c251d877c8826c2a8edb6e977c4e54545", "ref": "uat-preprod", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21541, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-14T06:44:19.917Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "64158dc4b1aa077cfa0f239ef2576f2d0c3e1ecc", "commit_to": "7d43d33c251d877c8826c2a8edb6e977c4e54545", "ref": "uat", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21540, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-14T06:38:01.210Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0d354aea98f735b90b99c1041a3afd7b779f525f", "commit_to": "a088b2ff18989e6ebf25acaf79ad56d7d05d51de", "ref": "develop", "commit_title": "fix set new login id", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21246, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-03T02:43:17.797Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "806a21707de4c91e7bd1d025a19d505905a48506", "commit_to": "ae730fefb17e1646f74fe2da410aaea07ddc412a", "ref": "develop", "commit_title": "fix test case, and report", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21242, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-07-03T01:30:01.365Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "85e13936dd8b69dfbfbb52bf6d25fec0cf39dd57", "commit_to": "806a21707de4c91e7bd1d025a19d505905a48506", "ref": "develop", "commit_title": "Merge branch 'develop' of *************:carina/carina-rest into dev...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21188, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-30T01:40:17.818Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 68, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "403348e6056ece16d5eff8f1a8c6ba28a5e6f349", "ref": "johnson-jdk17", "commit_title": "Translation remove contact no", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 21089, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-24T06:29:38.325Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "78943531869a19b8b9847e5bf7d548f6adce6a44", "commit_to": "16fe56874fc524bfb414aedbc92c21308a2c03a1", "ref": "develop", "commit_title": "minor fix on the flow of subscriber request", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20865, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-16T05:44:38.638Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "552d88f9463b4997e292d00703f2aee5cc2741ec", "commit_to": "70b07aa5f3721d1acb14ebdbf75bcb7f646043a3", "ref": "develop", "commit_title": "chart minor fix", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20817, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-12T07:14:51.613Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "225685882f8164c29f601e4b80bf733dbaac184c", "commit_to": "dc0f65b3a7a084f75f647f6bdd657597cc5567fe", "ref": "develop", "commit_title": "fix reset pin logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20808, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-12T06:52:01.523Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "e06111e3f7b5b273b50233a6ea655847a6ea653c", "commit_to": "225685882f8164c29f601e4b80bf733dbaac184c", "ref": "develop", "commit_title": "Merge branch 'develop' of *************:carina/carina-rest into dev...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20759, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-10T06:55:29.061Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e0a609e51304ee443967560acfc87f67e7ef295b", "commit_to": "552d88f9463b4997e292d00703f2aee5cc2741ec", "ref": "develop", "commit_title": "fix audit module not working", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20755, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-10T06:28:18.613Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "723d468ea9b319d8e3ea8ae366c224342c4d5528", "commit_to": "e0a609e51304ee443967560acfc87f67e7ef295b", "ref": "develop", "commit_title": "fix audit module not working", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20751, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-10T06:16:03.724Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "7387dfe382a2c45e05a7f98694cba71088a533b8", "commit_to": "723d468ea9b319d8e3ea8ae366c224342c4d5528", "ref": "develop", "commit_title": "fix mat option", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20742, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-10T05:29:31.702Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "573b78c3731a8dba975f790a40aaf2725cb03c6f", "commit_to": "7387dfe382a2c45e05a7f98694cba71088a533b8", "ref": "develop", "commit_title": "add stats json", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20738, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-10T04:07:07.925Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ddde187c1efb50bf4ce6e7138d2a0a838c3828dd", "commit_to": "573b78c3731a8dba975f790a40aaf2725cb03c6f", "ref": "develop", "commit_title": "fix license list import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20698, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-10T01:29:52.552Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "06129a1cb3d1889b6e907fb179f3b8086924c935", "commit_to": "ddde187c1efb50bf4ce6e7138d2a0a838c3828dd", "ref": "develop", "commit_title": "add new logo", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20576, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-06-04T06:49:25.560Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "95a06e3866c2ac073bb1aa5cae1da8569c91b796", "commit_to": "e16804d99cf6a97ecf93fb462b8905cee6571c00", "ref": "develop", "commit_title": "remove not blank for request", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20234, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-22T06:40:53.838Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3d5357b7f727501696b0b6668c318d246fd74403", "commit_to": "b47150e4aa8197d879038e54baf149249d3047d8", "ref": "develop", "commit_title": "fix update license api path and noti ext file", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20180, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-20T03:41:41.633Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "416b493ef0f5391757b2e71f265b1e866d25d926", "commit_to": "06129a1cb3d1889b6e907fb179f3b8086924c935", "ref": "develop", "commit_title": "fix spline chart tooltip display", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20179, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-20T03:28:23.283Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b29380a6900f37cd0ab3c421c4bae919a6b7be95", "commit_to": "416b493ef0f5391757b2e71f265b1e866d25d926", "ref": "develop", "commit_title": "add dashboard feature, add record history", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20175, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-20T00:55:04.780Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "4286558eab85afd243c5ae963d3f6034733079b5", "commit_to": "3d5357b7f727501696b0b6668c318d246fd74403", "ref": "develop", "commit_title": "fix daily hourly date format when query", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20171, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-19T08:41:06.200Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d270d50cae2fffe2786c52a778e3462b5c63e08b", "commit_to": "4286558eab85afd243c5ae963d3f6034733079b5", "ref": "develop", "commit_title": "update test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20169, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-19T08:30:29.731Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "17f413f0c1b88444601d3ae437d9280f8fad1026", "commit_to": "d270d50cae2fffe2786c52a778e3462b5c63e08b", "ref": "develop", "commit_title": "update test and query zone id", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20167, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-19T07:41:15.516Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ca48da6556495efd88b535a4b006855a647106a8", "commit_to": "17f413f0c1b88444601d3ae437d9280f8fad1026", "ref": "develop", "commit_title": "update test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20166, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-19T07:36:02.051Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "55f2e85641b8f993e15ed162de2ed2cec2c0ff80", "commit_to": "ca48da6556495efd88b535a4b006855a647106a8", "ref": "develop", "commit_title": "update utc zone when query", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20135, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-16T03:12:30.756Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ca44c05209e7a3c6685f7d4387e7adec5ac174f4", "commit_to": "6aab9d22f087384265f31b3f121e0a7db4c18734", "ref": "alif-debug-temp", "commit_title": "add to uri to avoild decode issue", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20133, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-16T03:10:06.560Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 10, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "ca44c05209e7a3c6685f7d4387e7adec5ac174f4", "ref": "alif-debug-temp", "commit_title": "Merge remote-tracking branch 'origin/3.1.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20007, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-13T06:12:14.679Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "625c823645421bbfd469170806f24084dfd895ec", "commit_to": "ae1afdbf715c920d79c4c3e4fd8ab705cd89413f", "ref": "develop", "commit_title": "remove unsed import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20006, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-13T06:09:53.137Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ee19eeacabcfc7197a874918438602f3c8bd1801", "commit_to": "6844fc16aad01eb1bdd7a0550f70a71c5687ee4d", "ref": "demo-card-tap", "commit_title": "remove duplicated card checker", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 20001, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-13T03:39:11.303Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0777155581db6f6d58d6916a8716a7e148a42456", "commit_to": "55f2e85641b8f993e15ed162de2ed2cec2c0ff80", "ref": "develop", "commit_title": "minor fix", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19997, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-13T03:35:06.767Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "826530a6edd485e9cc2d44a92b48ec5af55f35a8", "commit_to": "0777155581db6f6d58d6916a8716a7e148a42456", "ref": "develop", "commit_title": "allow remaining expriy days accept null", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19974, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-09T07:15:27.633Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "bb85f25cb05934485f7fee867de42405a5d6dc99", "commit_to": "826530a6edd485e9cc2d44a92b48ec5af55f35a8", "ref": "develop", "commit_title": "fix checkstyle", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19973, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-09T07:12:52.037Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e965a68e24c74151978d481b5dd66611b62b9608", "commit_to": "bb85f25cb05934485f7fee867de42405a5d6dc99", "ref": "develop", "commit_title": "fix checkstyle", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19972, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-09T07:00:11.974Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "64bf22caf03afd8f42ac3ab22cd6006f18386b38", "commit_to": "e965a68e24c74151978d481b5dd66611b62b9608", "ref": "develop", "commit_title": "add dashboard apis", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19923, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-06T07:30:01.599Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "c607327e0a1943a520ac7a5b84650926ea812ccf", "commit_to": "64bf22caf03afd8f42ac3ab22cd6006f18386b38", "ref": "develop", "commit_title": "remove comment", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19922, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-05-06T07:26:13.892Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "8b774b9297994176429620411f94146c38eb8030", "commit_to": "c607327e0a1943a520ac7a5b84650926ea812ccf", "ref": "develop", "commit_title": "Merge branch 'develop' of http://*************/nlicense/nlicense-co...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19838, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-30T01:35:52.982Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3fe115ca16a714d58c78b558a60405328543eb5b", "commit_to": "ee19eeacabcfc7197a874918438602f3c8bd1801", "ref": "develop", "commit_title": "minor fix, remark: to be delete", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19837, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-30T01:35:31.058Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3fe115ca16a714d58c78b558a60405328543eb5b", "commit_to": "ee19eeacabcfc7197a874918438602f3c8bd1801", "ref": "demo-card-tap", "commit_title": "minor fix, remark: to be delete", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19832, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T09:13:15.001Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "1881483ebbd6948b6db04513905d14e7562ad5ea", "commit_to": "3fe115ca16a714d58c78b558a60405328543eb5b", "ref": "develop", "commit_title": "fix checkstyle and sonar", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19831, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T09:12:50.887Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "1881483ebbd6948b6db04513905d14e7562ad5ea", "commit_to": "3fe115ca16a714d58c78b558a60405328543eb5b", "ref": "demo-card-tap", "commit_title": "fix checkstyle and sonar", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19830, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T09:08:27.732Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b0949b75201fc9bcfe7b9ad1510d851c15be9d8c", "commit_to": "1881483ebbd6948b6db04513905d14e7562ad5ea", "ref": "develop", "commit_title": "add userno field to demo card", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19829, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T09:08:06.488Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b0949b75201fc9bcfe7b9ad1510d851c15be9d8c", "commit_to": "1881483ebbd6948b6db04513905d14e7562ad5ea", "ref": "demo-card-tap", "commit_title": "add userno field to demo card", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19827, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T08:59:39.666Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 4, "action": "pushed", "ref_type": "branch", "commit_from": "3325b80d1659c9cafe66dc60de1a88afc89b666f", "commit_to": "7e1a2bafacf6673e9b75f18afecc952a72dc9198", "ref": "demo-card-tap", "commit_title": "merge branch, remark: to be deleted", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19825, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T08:06:45.426Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ab3842e7806e737e353b07be68ec9f48046f1707", "commit_to": "7e1a2bafacf6673e9b75f18afecc952a72dc9198", "ref": "develop", "commit_title": "merge branch, remark: to be deleted", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19824, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T08:05:57.948Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "b0949b75201fc9bcfe7b9ad1510d851c15be9d8c", "commit_to": "ab3842e7806e737e353b07be68ec9f48046f1707", "ref": "develop", "commit_title": "merge branch, remark: to be deleted", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19823, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T07:59:27.504Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "962668d5cf9d7e832db901c1b49d651620cdf423", "commit_to": "3325b80d1659c9cafe66dc60de1a88afc89b666f", "ref": "demo-card-tap", "commit_title": "fix add logic validate userno, remark: to be deleted", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19822, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T07:50:17.769Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "71a98c2d0930c056a10d2339c9d91e5e5109858c", "commit_to": "962668d5cf9d7e832db901c1b49d651620cdf423", "ref": "demo-card-tap", "commit_title": "demo add card update if duplicate card no, ignore remark: to be del...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19816, "project_id": 430, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T06:46:34.743Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e888c66a97e7525e88f867ba7a0491e3327acd79", "commit_to": "de146d92480c8c568a624e711e6a996eb9724a0c", "ref": "demo-card-tap", "commit_title": "demo add card, remark: should remove or to be delete in future", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19815, "project_id": 430, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-29T05:26:40.709Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 49, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "e888c66a97e7525e88f867ba7a0491e3327acd79", "ref": "demo-card-tap", "commit_title": "Remove additional filter", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19769, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-25T02:33:20.304Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "5cf10dbb57d86ef4b12e1172505e27040845daeb", "commit_to": "647fba60a0b425b6342fdd354a60c912da2b428f", "ref": "develop", "commit_title": "fix service test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19761, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-24T09:15:13.386Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "06fddee6bb94bc4a9c0415c89c00a784baa0ac47", "commit_to": "5cf10dbb57d86ef4b12e1172505e27040845daeb", "ref": "develop", "commit_title": "Merge branch 'develop' of http://*************/nlicense/nlicense-co...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19757, "project_id": 432, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-24T08:26:44.959Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "2b11be9ebc23b285ad00183fcb6a9aff7c0d7271", "commit_to": "440c682bf8d801f86c58456ad5e175929a296249", "ref": "develop", "commit_title": "Merge branch 'develop' of http://*************/nlicense/nlicense-sd...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19734, "project_id": 433, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-24T01:48:43.529Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 4, "action": "pushed", "ref_type": "branch", "commit_from": "791ea64ff4249bf021a0d16c0efb7854766799cb", "commit_to": "f3ec610a39b2e2457178d515afe5c09f99f3eb60", "ref": "johnson-dev", "commit_title": "Merge remote-tracking branch 'origin/main' into johnson-dev", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19703, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-23T07:38:13.086Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "c3995b821384606cc3242cca059374c51b5cca3d", "commit_to": "b29380a6900f37cd0ab3c421c4bae919a6b7be95", "ref": "develop", "commit_title": "minor fix and add logo place holder", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19702, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-23T07:37:16.988Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "61a5143129b28aab9f5e0084a87b8dce125ff9db", "commit_to": "2f7df6f18eca38d3296e4d7de1e008260295cfe8", "ref": "develop", "commit_title": "add notify service and feature", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19653, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-21T03:21:12.965Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 10, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "ca44c05209e7a3c6685f7d4387e7adec5ac174f4", "ref": "jdk-17", "commit_title": "Merge remote-tracking branch 'origin/3.1.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19567, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-14T03:45:18.069Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "20774672e8e1db58a8962b1202f288bc8db86605", "commit_to": "c3995b821384606cc3242cca059374c51b5cca3d", "ref": "develop", "commit_title": "update url, minor fix ui", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19558, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-14T03:26:20.223Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9d77f56c3ea9ffecadec93e63ba8fb3380df8efb", "commit_to": "61a5143129b28aab9f5e0084a87b8dce125ff9db", "ref": "develop", "commit_title": "update admin api path", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19526, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-11T09:02:54.738Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "bef6b68ff4000b1b6c42c2edd4bc66e67f150b0d", "commit_to": "20774672e8e1db58a8962b1202f288bc8db86605", "ref": "develop", "commit_title": "add license module", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19508, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-11T03:37:22.317Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "cf0235082a774e4a1a206dd3a0358fb4a082bfb4", "commit_to": "9d77f56c3ea9ffecadec93e63ba8fb3380df8efb", "ref": "develop", "commit_title": "update security", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19503, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-11T03:16:05.220Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9b24e2ad9f1f85e50d1b3323e91bde02a1ceac46", "commit_to": "cf0235082a774e4a1a206dd3a0358fb4a082bfb4", "ref": "develop", "commit_title": "update policy", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19498, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-11T02:33:18.436Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "90455e55495ed3abc75daf4d3610cdb2bed2a503", "commit_to": "9b24e2ad9f1f85e50d1b3323e91bde02a1ceac46", "ref": "develop", "commit_title": "add update license, fix test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19487, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-10T09:47:38.717Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "210133f2e72c82c2f92ef9fd463e5829af00fe7e", "commit_to": "90455e55495ed3abc75daf4d3610cdb2bed2a503", "ref": "develop", "commit_title": "update search query", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19466, "project_id": 305, "action_name": "accepted", "target_id": 1025, "target_iid": 8, "target_type": "MergeRequest", "author_id": 11, "target_title": "Pre master", "created_at": "2025-04-10T04:07:07.225Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19465, "project_id": 305, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-10T04:07:07.112Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 8, "action": "pushed", "ref_type": "branch", "commit_from": "e74abd0d4e51e9f48fefaeeb56d289363e10671c", "commit_to": "69c53804aaa1a3b35691ce888fe70ad0717b13d7", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19464, "project_id": 305, "action_name": "approved", "target_id": 1025, "target_iid": 8, "target_type": "MergeRequest", "author_id": 11, "target_title": "Pre master", "created_at": "2025-04-10T04:07:02.057Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19463, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-10T04:06:59.921Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 26, "action": "pushed", "ref_type": "branch", "commit_from": "65aa4071cd7992bc09c47b42d0ece4e73b828fda", "commit_to": "761f0fd5ec6ae91cb2d54ea5e4b201d48ef50ff2", "ref": "master", "commit_title": "Merge branch 'premaster' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19462, "project_id": 306, "action_name": "accepted", "target_id": 1024, "target_iid": 22, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster", "created_at": "2025-04-10T04:06:59.519Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19461, "project_id": 306, "action_name": "approved", "target_id": 1024, "target_iid": 22, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster", "created_at": "2025-04-10T04:06:55.817Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19449, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-10T03:37:13.659Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "49fa3e06967a5e57ab0209fe8144aba0bcd96e1d", "commit_to": "210133f2e72c82c2f92ef9fd463e5829af00fe7e", "ref": "develop", "commit_title": "update licence info", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19438, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-10T02:18:26.908Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d2c2e038abb3a8c4d7e170da57b995bdb5e5bc92", "commit_to": "49fa3e06967a5e57ab0209fe8144aba0bcd96e1d", "ref": "develop", "commit_title": "update license service test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19435, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-10T01:30:30.229Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "989c5fd33f156f8323587fd188b47c3f93353a52", "commit_to": "d2c2e038abb3a8c4d7e170da57b995bdb5e5bc92", "ref": "develop", "commit_title": "update read api comsume type", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19385, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-09T02:47:53.941Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "2ec3d57c3290ba14d8884eeb1fba579013df25d6", "commit_to": "989c5fd33f156f8323587fd188b47c3f93353a52", "ref": "develop", "commit_title": "fix request date format", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19382, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-09T01:34:39.463Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b7b143ae1ca2f31e841d65327d4d04ea2d51188b", "commit_to": "2ec3d57c3290ba14d8884eeb1fba579013df25d6", "ref": "develop", "commit_title": "fix controller", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19381, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-09T01:00:41.610Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "6ca83780c149f7ec91db132239f5d88d23adfb1d", "commit_to": "b7b143ae1ca2f31e841d65327d4d04ea2d51188b", "ref": "develop", "commit_title": "fix sonar", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19359, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-08T06:15:46.206Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "cc97ab98191a1538bbbe163f860226ab60459595", "commit_to": "6ca83780c149f7ec91db132239f5d88d23adfb1d", "ref": "develop", "commit_title": "fix checkstyle", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19357, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-08T06:11:15.231Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "69cf6b88b4d18b2ba13d2489422d70bd587e7f03", "commit_to": "cc97ab98191a1538bbbe163f860226ab60459595", "ref": "develop", "commit_title": "add get license api", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19347, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-08T01:56:06.221Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "982914000be6a1007a68ef8cdbfc1678285fd18f", "commit_to": "69cf6b88b4d18b2ba13d2489422d70bd587e7f03", "ref": "develop", "commit_title": "change plugin version", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19344, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-08T01:16:25.015Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "8bef01218d06e18c30a2b07ad653985592c9a27f", "commit_to": "982914000be6a1007a68ef8cdbfc1678285fd18f", "ref": "develop", "commit_title": "test plugin version", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19343, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-08T01:08:21.873Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "673bd60018f069b37ef513ab40e3113f8c46539d", "commit_to": "8bef01218d06e18c30a2b07ad653985592c9a27f", "ref": "develop", "commit_title": "update dependedcies version", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19342, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-08T00:55:29.069Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "112aace18e7f71676145020489f36ccdf0f920c5", "commit_to": "673bd60018f069b37ef513ab40e3113f8c46539d", "ref": "develop", "commit_title": "fix checkstyle", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19341, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T09:13:39.826Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f4a58620c741611c69e94ed8ecaab1fb13561524", "commit_to": "112aace18e7f71676145020489f36ccdf0f920c5", "ref": "develop", "commit_title": "fix checksty;e", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19340, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T09:08:11.225Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "81567c0f700b83a5663d5f4b31f929e7fc0d0fe9", "commit_to": "f4a58620c741611c69e94ed8ecaab1fb13561524", "ref": "develop", "commit_title": "fix sonar", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19333, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T08:42:49.820Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "17446bec69ed2b4b16c6117d073aea117931c74b", "commit_to": "81567c0f700b83a5663d5f4b31f929e7fc0d0fe9", "ref": "develop", "commit_title": "temp downgrade plugin version for rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19332, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T08:35:50.730Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "abb428cf5b1aa9d8867d5648c21d341e7ac44950", "commit_to": "17446bec69ed2b4b16c6117d073aea117931c74b", "ref": "develop", "commit_title": "update plugins version", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19330, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T08:15:55.343Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d3708c8803bbf413e90c0bcf52f543930a7a15d1", "commit_to": "abb428cf5b1aa9d8867d5648c21d341e7ac44950", "ref": "develop", "commit_title": "remove unused import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19329, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T08:10:42.873Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "070da794985a97669b82695fc4dbe58c11cb3671", "commit_to": "d3708c8803bbf413e90c0bcf52f543930a7a15d1", "ref": "develop", "commit_title": "fix sonar", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19327, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T08:04:20.382Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "2b7df945e93fa0e259582fed7732dfabe3b74b1a", "commit_to": "070da794985a97669b82695fc4dbe58c11cb3671", "ref": "develop", "commit_title": "Merge branch 'develop' of http://*************/nlicense/nlicense-co...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19309, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T05:02:42.209Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ed86199f95a04f88020e0d9215443d9a24f00a45", "commit_to": "fec77694d5bf8ef82ef30f9eaf201943f051f53e", "ref": "develop", "commit_title": "remove unsed code", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19308, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T04:57:55.603Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0118d5406bcd4a513bc068c526edb06220de8322", "commit_to": "ed86199f95a04f88020e0d9215443d9a24f00a45", "ref": "develop", "commit_title": "remove unsed import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19307, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T04:50:43.281Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f6c35f1a2a007e2412c6f275353ba26434093072", "commit_to": "0118d5406bcd4a513bc068c526edb06220de8322", "ref": "develop", "commit_title": "remove unsed import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19297, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T03:58:49.914Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "cfd996631241d70c965be44bec39fa06dc156001", "commit_to": "f6c35f1a2a007e2412c6f275353ba26434093072", "ref": "develop", "commit_title": "remove unsed import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19296, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-07T03:50:47.321Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e1d175670b483871ce64a2bca7e92269eba8c20b", "commit_to": "cfd996631241d70c965be44bec39fa06dc156001", "ref": "develop", "commit_title": "add new feature", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19269, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-04T03:51:40.798Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "239f749847dc55ad8ad26564080c1d52884c9b89", "commit_to": "bef6b68ff4000b1b6c42c2edd4bc66e67f150b0d", "ref": "develop", "commit_title": "fix import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19268, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-04T03:05:37.592Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0d84f7662da0f0a34a826b0b3e10cca3b56e7b87", "commit_to": "239f749847dc55ad8ad26564080c1d52884c9b89", "ref": "develop", "commit_title": "fix import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19267, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-04T02:54:06.248Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9dfe66bbf0e9dc07726ffd307f08316dbaeae2b6", "commit_to": "0d84f7662da0f0a34a826b0b3e10cca3b56e7b87", "ref": "develop", "commit_title": "fix import", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19264, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-04T02:33:56.911Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "483c8eed9a64c8344ac8e18e71c2ac47d68bc261", "commit_to": "9dfe66bbf0e9dc07726ffd307f08316dbaeae2b6", "ref": "develop", "commit_title": "updat bootbuildimage to make sure all js is included", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19263, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-04T02:11:53.894Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "c4de7c3154c904875d7ba9654abcdf15941b78fe", "commit_to": "e1d175670b483871ce64a2bca7e92269eba8c20b", "ref": "develop", "commit_title": "update config", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19262, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-04T02:09:47.957Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "eecd11d04a80dfcc576054f29ba0156098f7e37f", "commit_to": "483c8eed9a64c8344ac8e18e71c2ac47d68bc261", "ref": "develop", "commit_title": "update auth encryption", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19220, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-03T03:34:34.441Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "7b62aab14f960c77538fdced08ed1fed23d0a466", "commit_to": "eecd11d04a80dfcc576054f29ba0156098f7e37f", "ref": "develop", "commit_title": "fix gradle settings, update recaptcha", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19210, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-03T02:19:08.277Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "591424b9b9e27d13b5da3bee7547b7c5eaee885b", "commit_to": "7b62aab14f960c77538fdced08ed1fed23d0a466", "ref": "develop", "commit_title": "fix gradle settings", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19209, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-03T02:00:06.732Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b353e96ccaa0c130ce9c307e419d69f53627c2c7", "commit_to": "591424b9b9e27d13b5da3bee7547b7c5eaee885b", "ref": "develop", "commit_title": "fix gradle settings", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19208, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-03T01:52:20.775Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "de2fe3e614f98440696f9ac858fc7a78cf9b6612", "commit_to": "b353e96ccaa0c130ce9c307e419d69f53627c2c7", "ref": "develop", "commit_title": "fix project name", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19207, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-04-03T01:43:49.711Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "abac1b344c8cf1c31f97efff1eb0b4fb5832e656", "commit_to": "de2fe3e614f98440696f9ac858fc7a78cf9b6612", "ref": "develop", "commit_title": "fix project name", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19166, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T03:41:45.015Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "373a8053c08affd7c3cba0ab613fc50ae01e2f9e", "commit_to": "ae134e7dd58c6b0b8e7e409353155a5a89cc4aea", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19165, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T03:23:40.704Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "pushed", "ref_type": "branch", "commit_from": "1ea892cd564cde616aefa1092675780f72441e51", "commit_to": "373a8053c08affd7c3cba0ab613fc50ae01e2f9e", "ref": "develop", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19164, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T03:17:59.915Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d6d0ef0b5da258a7c1cbd294131f157ca7b29705", "commit_to": "1ea892cd564cde616aefa1092675780f72441e51", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19163, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T03:16:00.442Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "99f8c97f8edbf2293c6936497242e5332d34b18b", "commit_to": "d6d0ef0b5da258a7c1cbd294131f157ca7b29705", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19162, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T03:08:17.676Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "2de39d088721077172b2911b20f847441c1c4413", "commit_to": "99f8c97f8edbf2293c6936497242e5332d34b18b", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19161, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T02:54:11.928Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "02e96f297aec67b0f2402dd9a80b3db8c6ddc629", "commit_to": "2de39d088721077172b2911b20f847441c1c4413", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19160, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T02:42:53.109Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ec7f5346f111bb3e2fcfc2f69330f271d3bcf808", "commit_to": "02e96f297aec67b0f2402dd9a80b3db8c6ddc629", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19158, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T02:25:31.170Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "26d92181dec781366db94923c3db07882540f6b5", "commit_to": "ec7f5346f111bb3e2fcfc2f69330f271d3bcf808", "ref": "develop", "commit_title": "aot rnd", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19150, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T01:54:57.773Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "bad4b9a67ac8832a16a09f110aeb6b04bacaada9", "commit_to": "26d92181dec781366db94923c3db07882540f6b5", "ref": "develop", "commit_title": "test build wihtout aot", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19147, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T01:28:31.625Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "11b2d64ed9c5f4d090fea17442b55f2ae8c5be1f", "commit_to": "bad4b9a67ac8832a16a09f110aeb6b04bacaada9", "ref": "develop", "commit_title": "test build wihtout aot", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19143, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T01:22:13.602Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "509a213b967992ab75b6aa33d52d733bf70fe11c", "commit_to": "11b2d64ed9c5f4d090fea17442b55f2ae8c5be1f", "ref": "develop", "commit_title": "test build wihtout aot", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19142, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-28T01:16:38.793Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "373a8053c08affd7c3cba0ab613fc50ae01e2f9e", "commit_to": "509a213b967992ab75b6aa33d52d733bf70fe11c", "ref": "develop", "commit_title": "test build wihtout aot", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19138, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T09:57:24.997Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "09f17b9c911480d716cf60147522f68860ca9133", "commit_to": "373a8053c08affd7c3cba0ab613fc50ae01e2f9e", "ref": "develop", "commit_title": "fix gradle task", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19136, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T09:46:41.834Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "bcd21d7b856fa7d770a2b72954db3e15f6f42691", "commit_to": "09f17b9c911480d716cf60147522f68860ca9133", "ref": "develop", "commit_title": "fix gradle task", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19133, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T09:01:40.692Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "fcb53b0b00eb557fe9229cf53ac6fb039293bd46", "commit_to": "bcd21d7b856fa7d770a2b72954db3e15f6f42691", "ref": "develop", "commit_title": "include csp", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19132, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T08:52:18.778Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "413a95df7c27524f2584f9bc6dbee3aff90d4397", "commit_to": "fcb53b0b00eb557fe9229cf53ac6fb039293bd46", "ref": "develop", "commit_title": "fix properties file path", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19124, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T07:36:19.955Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0ec50576208633490202a2e57f96f2d886bb593e", "commit_to": "413a95df7c27524f2584f9bc6dbee3aff90d4397", "ref": "develop", "commit_title": "fix build.gradle", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19122, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T07:27:49.400Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "84653a5296594087abef07137b9df9c953b9e9d6", "commit_to": "0ec50576208633490202a2e57f96f2d886bb593e", "ref": "develop", "commit_title": "update url", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19121, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T07:20:19.286Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "43d9db8272ad50cbab134992e258e5887018f27c", "commit_to": "84653a5296594087abef07137b9df9c953b9e9d6", "ref": "develop", "commit_title": "move plugin management to top", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19120, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T07:17:38.513Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "02dc0d9908fa3d79848259bd3848bddf41f6570f", "commit_to": "43d9db8272ad50cbab134992e258e5887018f27c", "ref": "develop", "commit_title": "fix repo settings", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19119, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T06:58:47.376Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "862100d03b497fff2d767d8f5e0dac815df7c0de", "commit_to": "02dc0d9908fa3d79848259bd3848bddf41f6570f", "ref": "develop", "commit_title": "remove build.gradle do first", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19118, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T06:49:06.042Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "03db1a5d78f2c619c75d57352d188f26bc283a88", "commit_to": "862100d03b497fff2d767d8f5e0dac815df7c0de", "ref": "develop", "commit_title": "fixed config file", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19113, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T06:40:16.145Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "1c933b0fc9e911b05e4a815405d34d084befb0a8", "commit_to": "03db1a5d78f2c619c75d57352d188f26bc283a88", "ref": "develop", "commit_title": "test jenkins", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19110, "project_id": 454, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T06:26:10.791Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ebe398706a691da24585baef457fbc476f123555", "commit_to": "1c933b0fc9e911b05e4a815405d34d084befb0a8", "ref": "develop", "commit_title": "init", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19104, "project_id": 306, "action_name": "accepted", "target_id": 1021, "target_iid": 21, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster", "created_at": "2025-03-27T03:43:48.073Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19103, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-27T03:43:48.036Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 5, "action": "pushed", "ref_type": "branch", "commit_from": "abe79661d1edde88907100ddb8aafe2fe5fba329", "commit_to": "65aa4071cd7992bc09c47b42d0ece4e73b828fda", "ref": "master", "commit_title": "Merge branch 'premaster' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19102, "project_id": 306, "action_name": "approved", "target_id": 1021, "target_iid": 21, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster", "created_at": "2025-03-27T03:43:21.640Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19085, "project_id": 432, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-26T06:11:15.096Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f6f9242a65e12b2e1c835a3bd6c540f88bc81962", "commit_to": "fae90ead94891a8c747afeb58b718b9ed563b963", "ref": "develop", "commit_title": "test jenkins", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19084, "project_id": 431, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-26T05:56:47.764Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "c24f6a11ee9ef5efed667d8f51a70c9e12687790", "commit_to": "c4de7c3154c904875d7ba9654abcdf15941b78fe", "ref": "develop", "commit_title": "remove private key from license info", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19065, "project_id": 433, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-26T03:26:50.098Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "791ea64ff4249bf021a0d16c0efb7854766799cb", "ref": "johnson-dev", "commit_title": "Initial commit for johnson-dev branch", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19063, "project_id": 454, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-26T03:23:18.214Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "ebe398706a691da24585baef457fbc476f123555", "ref": "develop", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 19062, "project_id": 454, "action_name": "created", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-26T03:07:57.621Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 19038, "project_id": 416, "action_name": "joined", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-25T01:37:29.476Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 18673, "project_id": 452, "action_name": "joined", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-17T05:29:23.315Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 18445, "project_id": 448, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-10T09:04:59.482Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "eb7c3145ae79b6f3a380b8bc4f31ec313d3c46ba", "commit_to": "fed4f84ebad324b0ebd84c11825d44156a9c9b94", "ref": "johnson-dev", "commit_title": "logout temp push", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 18424, "project_id": 432, "action_name": "joined", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-10T06:22:28.512Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 18423, "project_id": 431, "action_name": "joined", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-10T06:22:16.431Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 18422, "project_id": 433, "action_name": "joined", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-10T06:22:02.502Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 18229, "project_id": 448, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-03-03T03:21:44.909Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "90f97d0fbb2459c1f67ba37aa9ae313652de32aa", "commit_to": "eb7c3145ae79b6f3a380b8bc4f31ec313d3c46ba", "ref": "johnson-dev", "commit_title": "add txn confirm and result page", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 18181, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-02-28T03:02:07.768Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "8bb7e9ea7e081094c9b68ccfe2223031937f114d", "commit_to": "605cb99aa17112fa7ccceabe5666a419fa4661ed", "ref": "3.1.0", "commit_title": "add comment", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17930, "project_id": 448, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-02-21T07:35:05.026Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "dfd0a7af6968c92c17fdcd59409db89ceea50d35", "commit_to": "90f97d0fbb2459c1f67ba37aa9ae313652de32aa", "ref": "johnson-dev", "commit_title": "add contact us", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17890, "project_id": 448, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-02-20T08:20:02.565Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3002484c5a0eac8bbda82bd1312ca3ad259103a4", "commit_to": "dfd0a7af6968c92c17fdcd59409db89ceea50d35", "ref": "johnson-dev", "commit_title": "add noti, home, change lang", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17767, "project_id": 448, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-02-18T02:58:41.867Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "3002484c5a0eac8bbda82bd1312ca3ad259103a4", "ref": "johnson-dev", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17657, "project_id": 305, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-02-13T04:12:17.893Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 12, "action": "pushed", "ref_type": "branch", "commit_from": "8d2a0f2907c2be8f8803aa7400e88890b7e4bff0", "commit_to": "e74abd0d4e51e9f48fefaeeb56d289363e10671c", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17656, "project_id": 305, "action_name": "accepted", "target_id": 975, "target_iid": 7, "target_type": "MergeRequest", "author_id": 11, "target_title": "Pre master", "created_at": "2025-02-13T04:12:17.577Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 17647, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-02-13T03:57:50.667Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 91, "action": "pushed", "ref_type": "branch", "commit_from": "19fdb29e2b8be341350cb90baf2c6210ab47cda8", "commit_to": "abe79661d1edde88907100ddb8aafe2fe5fba329", "ref": "master", "commit_title": "Merge branch 'premaster' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17646, "project_id": 306, "action_name": "accepted", "target_id": 973, "target_iid": 20, "target_type": "MergeRequest", "author_id": 11, "target_title": "Premaster", "created_at": "2025-02-13T03:57:47.694Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 17229, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-24T02:05:05.636Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 6, "action": "pushed", "ref_type": "branch", "commit_from": "67dc687525b0b3c4de6baa917a6d56a9ab7f439e", "commit_to": "920929cbf75943ad39e9ad989c2b9156169d6831", "ref": "uat", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17228, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-24T01:38:33.740Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d2611c11f245dcb5139384ac32ac4d5721d416a3", "commit_to": "4f48a833e8b8a2e5468d72e1048cf10571d263f1", "ref": "develop", "commit_title": "fix test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17227, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-24T01:31:28.024Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "ed66418ac522075235a4d55db07560521c3e4dee", "commit_to": "d2611c11f245dcb5139384ac32ac4d5721d416a3", "ref": "develop", "commit_title": "//vsps-cardId", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17109, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-21T01:48:41.963Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3364ee12c06a4d5b8448c2fa5f4ed770d67ad594", "commit_to": "eda533b06e4b2884d1fd38b9a5b46b8822a2530d", "ref": "develop", "commit_title": "update all response code vsps", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17108, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-21T01:48:21.506Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3364ee12c06a4d5b8448c2fa5f4ed770d67ad594", "commit_to": "eda533b06e4b2884d1fd38b9a5b46b8822a2530d", "ref": "vsps-3.0", "commit_title": "update all response code vsps", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17107, "project_id": 448, "action_name": "joined", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-21T01:26:29.131Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 17092, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-20T08:50:31.799Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "8934c0d907f1d1c0bacd269b6cc2c5e6f92ecfb8", "commit_to": "ff31d68d5bf1d7cc2101944b30aa880069cc1bc9", "ref": "develop", "commit_title": "Merge branch 'vsps-3.0' of *************:carina/carina-rest into vs...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17091, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-20T08:50:07.542Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "8934c0d907f1d1c0bacd269b6cc2c5e6f92ecfb8", "commit_to": "ff31d68d5bf1d7cc2101944b30aa880069cc1bc9", "ref": "vsps-3.0", "commit_title": "Merge branch 'vsps-3.0' of *************:carina/carina-rest into vs...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 17071, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-20T07:11:53.459Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 27, "action": "pushed", "ref_type": "branch", "commit_from": "ac2a63e18ac2b56828ba77eeafd133365fc849ef", "commit_to": "20dd259081885aaa04146aa990047abf4c90de1e", "ref": "vsps-3.0", "commit_title": "update logger logic", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16896, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-13T09:22:30.115Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "a0737928cefacf18a28e4544a90092410a41a746", "commit_to": "19fdb29e2b8be341350cb90baf2c6210ab47cda8", "ref": "master", "commit_title": "fix physical cart art upload", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16895, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-13T09:16:41.657Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "96b154f198742e709131526682f6a79eeb867475", "commit_to": "46c39185275dfc6776803677601f1a8f6be09833", "ref": "uat", "commit_title": "fix uploadPhysicalCardArtImage", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16894, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-13T08:32:30.581Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "8b1cd248bd6795a49d5a73e288205c9129ac21d9", "commit_to": "dd0ff4d24d5d63978acbb406ddd2ebeaf6a10223", "ref": "develop", "commit_title": "fix physical cart art upload and remove logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16893, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2025-01-13T08:11:01.204Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f4a08242599db66cbbd878faaf28b32e6eb7dfae", "commit_to": "8b1cd248bd6795a49d5a73e288205c9129ac21d9", "ref": "develop", "commit_title": "fix physical cart art upload", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16200, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-16T00:51:50.524Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "1f3cb2c93fdcd3b49b507cef44be414a609617a6", "commit_to": "dad0648bd2e0872b754f79fdc8ac4edba3111f42", "ref": "develop", "commit_title": "fix logger, and remove comment", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16061, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-06T03:07:33.618Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "87b37806173f9c7c4d14fa6efcb7acd67d160e7b", "commit_to": "1f3cb2c93fdcd3b49b507cef44be414a609617a6", "ref": "develop", "commit_title": "fix get cert and private key", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16060, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-06T02:49:47.369Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ee135a85097f693faab9b0476502c4d38cb332e6", "commit_to": "87b37806173f9c7c4d14fa6efcb7acd67d160e7b", "ref": "develop", "commit_title": "fix get cert", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16058, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-06T02:10:25.329Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "40295792d37d7fd19e21f49d73c6fa14990422d2", "commit_to": "ee135a85097f693faab9b0476502c4d38cb332e6", "ref": "develop", "commit_title": "fix cert url decode", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16054, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-05T08:32:37.863Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "a0fc037f91aecfb95bebbc544e0f29d498cae812", "commit_to": "40295792d37d7fd19e21f49d73c6fa14990422d2", "ref": "develop", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16052, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-05T08:10:36.960Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ca64f22548d8200f0de06c4829b13c2bab3f067e", "commit_to": "a0fc037f91aecfb95bebbc544e0f29d498cae812", "ref": "develop", "commit_title": "add logger for testing", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16011, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T08:37:24.322Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "42e98a24590060c86f6c8f370fb25e29a42ab379", "commit_to": "ca64f22548d8200f0de06c4829b13c2bab3f067e", "ref": "develop", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16010, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T08:36:00.744Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "864cc5fc687f0464ed14c408654d79ea4e808200", "commit_to": "ca64f22548d8200f0de06c4829b13c2bab3f067e", "ref": "vsps-3.0", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16003, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T07:44:56.059Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "42e98a24590060c86f6c8f370fb25e29a42ab379", "commit_to": "864cc5fc687f0464ed14c408654d79ea4e808200", "ref": "vsps-3.0", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16002, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T07:43:21.068Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "28cb49afef3fa8a7f8e7bfdde4f127abc8a62771", "commit_to": "42e98a24590060c86f6c8f370fb25e29a42ab379", "ref": "develop", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 16001, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T07:43:06.180Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "28cb49afef3fa8a7f8e7bfdde4f127abc8a62771", "commit_to": "42e98a24590060c86f6c8f370fb25e29a42ab379", "ref": "vsps-3.0", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15986, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T06:17:29.140Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "5b62489f85e8995355c1a1a123d0bf0acedc757e", "commit_to": "28cb49afef3fa8a7f8e7bfdde4f127abc8a62771", "ref": "develop", "commit_title": "handle null amount", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15985, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T06:17:14.828Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "5b62489f85e8995355c1a1a123d0bf0acedc757e", "commit_to": "28cb49afef3fa8a7f8e7bfdde4f127abc8a62771", "ref": "vsps-3.0", "commit_title": "handle null amount", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15982, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T05:39:12.931Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "75c7bf659e01b294f283cc14816e099f95762e3f", "commit_to": "5b62489f85e8995355c1a1a123d0bf0acedc757e", "ref": "develop", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15981, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T05:39:00.379Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 5, "action": "pushed", "ref_type": "branch", "commit_from": "fd36fab3bd7739ff7e7bad822bccd524c6ab8c0b", "commit_to": "5b62489f85e8995355c1a1a123d0bf0acedc757e", "ref": "vsps-3.0", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15974, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-04T03:26:56.356Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "5bb7e0be6064e2a7d91728818e313d9dbc883062", "commit_to": "75c7bf659e01b294f283cc14816e099f95762e3f", "ref": "develop", "commit_title": "fix vsps audit, http, flow", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15935, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-03T07:42:29.816Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "64978299bf2297488a37e92028a7f2035e38245f", "commit_to": "5bb7e0be6064e2a7d91728818e313d9dbc883062", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/vsps-3.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15934, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-03T07:42:07.222Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "accbecb302760beb790cd6864e5771a990aba760", "commit_to": "fd36fab3bd7739ff7e7bad822bccd524c6ab8c0b", "ref": "vsps-3.0", "commit_title": "remove unused code", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15926, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-03T06:58:22.992Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 24, "action": "pushed", "ref_type": "branch", "commit_from": "4a8327a8bd02527a00b8fd12f9e198e369db54a5", "commit_to": "64978299bf2297488a37e92028a7f2035e38245f", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/vsps-3.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15925, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-03T06:56:53.619Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e0bbcfef37fa7e15ba45fa3073600cfb20860dda", "commit_to": "accbecb302760beb790cd6864e5771a990aba760", "ref": "vsps-3.0", "commit_title": "add test, fix resource path, clean code, fix bugs, change function ...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15908, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-12-03T06:00:38.236Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9278c3edba8ebc76c8b1723a11b23e8cddc60a26", "commit_to": "e0bbcfef37fa7e15ba45fa3073600cfb20860dda", "ref": "vsps-3.0", "commit_title": "add test, fix resource path, clean code, fix bugs, change function ...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15728, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-29T03:55:06.881Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 13, "action": "pushed", "ref_type": "branch", "commit_from": "37a5af65c3fa357c6fd3af9a7f0ebb514a6b0206", "commit_to": "2352401f6ea252e21bf83165e014f126c3a50697", "ref": "vsps-3.0", "commit_title": "review and add comment", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 15035, "project_id": 306, "action_name": "accepted", "target_id": 913, "target_iid": 19, "target_type": "MergeRequest", "author_id": 11, "target_title": "2.5.1", "created_at": "2024-11-19T02:50:22.726Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 15034, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-19T02:50:22.417Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 5, "action": "pushed", "ref_type": "branch", "commit_from": "bf0a29c4279cff8c6aa8473a29548b80647399c6", "commit_to": "a0737928cefacf18a28e4544a90092410a41a746", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14953, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-18T05:16:40.526Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "d9141558de6d1a3d9247f83f428e9a6e71db2f69", "commit_to": "b40d86bc2a8152e045c2666859a7f1ed3227a31e", "ref": "uat", "commit_title": "bump version to 2.5.1", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14952, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-18T04:06:22.814Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "1b7ce27b3f52164d94edb08fb6b12f24dd16ed76", "commit_to": "4a8327a8bd02527a00b8fd12f9e198e369db54a5", "ref": "develop", "commit_title": "fix service fee flow validation", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14951, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-18T04:06:05.309Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "1b7ce27b3f52164d94edb08fb6b12f24dd16ed76", "commit_to": "4a8327a8bd02527a00b8fd12f9e198e369db54a5", "ref": "2.5.1", "commit_title": "fix service fee flow validation", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14950, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-18T03:59:51.387Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "1b7ce27b3f52164d94edb08fb6b12f24dd16ed76", "ref": "2.5.1", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14719, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-12T02:42:08.185Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "tag", "commit_from": null, "commit_to": "b99f48ee408437f4dd358fc62a837533d57005bf", "ref": "v2.5.0-release", "commit_title": "Bump Release 2.5.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14718, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-12T02:41:38.659Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 5, "action": "pushed", "ref_type": "branch", "commit_from": "3a3ad852afe3179c1bf1b90c7d65afa5ec6dda52", "commit_to": "bf0a29c4279cff8c6aa8473a29548b80647399c6", "ref": "master", "commit_title": "Bump Release 2.5.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14554, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-08T02:47:16.575Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "71670d96e63732de44c4cba970ccf6a377d0722f", "commit_to": "07cb930163d45a668ef1cbc59566cb2cd487667d", "ref": "vsps-3.0", "commit_title": "fix logger display", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14500, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-07T02:50:25.689Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e03f92889fdfe74d388130de08c4a7f20d0d4ea4", "commit_to": "d9141558de6d1a3d9247f83f428e9a6e71db2f69", "ref": "uat", "commit_title": "UAT Version 2.5.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14431, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-05T06:02:47.586Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "tag", "commit_from": null, "commit_to": "7c164a625f5abf0c8200ecc354867882473b4457", "ref": "v2.5.0-uat", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14330, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-04T07:10:05.290Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "ddaaa2c6559eedf8e6c7304303e6d438dc55f67d", "commit_to": "e03f92889fdfe74d388130de08c4a7f20d0d4ea4", "ref": "uat", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14329, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-04T07:09:01.541Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "37214566bb00619391fc01c084872fb5993e9f87", "commit_to": "1b7ce27b3f52164d94edb08fb6b12f24dd16ed76", "ref": "develop", "commit_title": "update sdk to 6.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14328, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-04T07:08:12.509Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "37214566bb00619391fc01c084872fb5993e9f87", "commit_to": "1b7ce27b3f52164d94edb08fb6b12f24dd16ed76", "ref": "vts-6.4.0", "commit_title": "update sdk to 6.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14321, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-11-04T05:52:12.361Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "37214566bb00619391fc01c084872fb5993e9f87", "ref": "vts-6.4.0", "commit_title": "Update gradle-git-properties plugin version", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14021, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-24T09:04:25.079Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d53f097d84ffd143821c2d96f7978acb3db0b738", "commit_to": "3a3ad852afe3179c1bf1b90c7d65afa5ec6dda52", "ref": "master", "commit_title": "bump release to 2.4.1", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14015, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-24T08:46:00.782Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 5, "action": "pushed", "ref_type": "branch", "commit_from": "c9f039629f4869206e5c21c9b9887ff77cbaf96b", "commit_to": "d53f097d84ffd143821c2d96f7978acb3db0b738", "ref": "master", "commit_title": "Merge remote-tracking branch 'origin/uat'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14006, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-24T08:11:30.259Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 4, "action": "pushed", "ref_type": "branch", "commit_from": "e4734e35c4aab0b6a5676acd3add6c65e890ec13", "commit_to": "ddaaa2c6559eedf8e6c7304303e6d438dc55f67d", "ref": "uat", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14004, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-24T07:58:52.243Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "0488dd79c0fdb01bf1883b62f4e636dabb89ae35", "commit_to": "60e12bbc8494bba0e46f83bd9f3533497c740b01", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 14003, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-24T07:58:00.505Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "eb1724a2a1c155fda0ec6c634dfc76dd373c5462", "commit_to": "c03f74b9156d8baa2e84d9fb2bcb5e6831649ad7", "ref": "2.4.0", "commit_title": "Merge branch '2.4.0' of *************:carina/carina-rest into 2.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13922, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T16:04:50.388Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "tag", "commit_from": null, "commit_to": "503ed073a126bee2cc9d7f3dc064c4bf68098d89", "ref": "v2.4.0-1", "commit_title": "Merge remote-tracking branch 'origin/pre-master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13921, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T16:03:56.453Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 187, "action": "pushed", "ref_type": "branch", "commit_from": "98f89c5ab507e0b09056d222d03ceb4a469310c8", "commit_to": "c9f039629f4869206e5c21c9b9887ff77cbaf96b", "ref": "master", "commit_title": "Merge remote-tracking branch 'origin/pre-master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13920, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T16:02:53.472Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 186, "action": "pushed", "ref_type": "branch", "commit_from": "bcb91e19511566c13fc706e5079a6921ebd4acb1", "commit_to": "f73d66847c361f8fa4ab39957289fb0df2406d53", "ref": "pre-master", "commit_title": "Bump Release 2.4.0-1", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13919, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T15:21:28.225Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "1db00c83271af01f67b25ff54cd5f2f3b3017397", "commit_to": "98f89c5ab507e0b09056d222d03ceb4a469310c8", "ref": "master", "commit_title": "Merge branch 'master' of *************:carina/carina-rest", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13918, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T15:20:37.784Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "created", "ref_type": "tag", "commit_from": null, "commit_to": "a1bd5b1496089ca2784b3bf1cd88d9f606a8a405", "ref": "v2.4.0", "commit_title": "Bump Release 2.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13917, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T15:20:11.107Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "de4146fe753a28359b9033af515a052aed8e96ce", "commit_to": "bcb91e19511566c13fc706e5079a6921ebd4acb1", "ref": "pre-master", "commit_title": "Bump Release 2.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13915, "project_id": 305, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-22T14:40:10.539Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 24, "action": "pushed", "ref_type": "branch", "commit_from": "91e71a34c1e3945a025c03f78aed9d912958559f", "commit_to": "8d2a0f2907c2be8f8803aa7400e88890b7e4bff0", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13914, "project_id": 305, "action_name": "accepted", "target_id": 875, "target_iid": 6, "target_type": "MergeRequest", "author_id": 11, "target_title": "e-statement, paper statement, profile picture, update cardholder email", "created_at": "2024-10-22T14:40:08.287Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 13913, "project_id": 305, "action_name": "approved", "target_id": 875, "target_iid": 6, "target_type": "MergeRequest", "author_id": 11, "target_title": "e-statement, paper statement, profile picture, update cardholder email", "created_at": "2024-10-22T14:39:38.723Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 13317, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-09T09:10:06.147Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "93a7a2f72ada755554aaf1359d3f507023bee066", "commit_to": "08347cba060d3f0933560968d343844285b7e4d6", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13316, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-09T09:09:49.033Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d8543b3d28e58e4bf11b1d95ca3c7a4f1d141cdf", "commit_to": "7629f54782153ea5a542627536f5703607cd7677", "ref": "2.4.0", "commit_title": "minor fix email format", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13273, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-09T05:37:17.553Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "c145f1385a5f9d1e1f5aa3f92a3ce5685a56eeb6", "commit_to": "93a7a2f72ada755554aaf1359d3f507023bee066", "ref": "develop", "commit_title": "Merge branch 'develop' of *************:carina/carina-rest into dev...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13272, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-09T05:36:17.704Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "8a48d29358425d07bf207b6b8dd0d33b80e019bf", "commit_to": "d8543b3d28e58e4bf11b1d95ca3c7a4f1d141cdf", "ref": "2.4.0", "commit_title": "fix according to new requirement, remove otp validation", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13147, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T09:34:36.659Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "a15bfb1b86390ab4b2027cbd61dd395524343f57", "commit_to": "3d57b8db9d87f371b783909f535e7050c2cfe076", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13146, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T09:34:15.130Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f0eb2620393065dd299d7be69e3857bdd7e1eb29", "commit_to": "8d3b51a1423e3971bb0841589e4e9d9936230e3c", "ref": "2.4.0", "commit_title": "clean up, add logout logic", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13134, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T07:43:41.668Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "370597a4a817ee66132cc49284e99734cabf7b25", "commit_to": "a15bfb1b86390ab4b2027cbd61dd395524343f57", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13133, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T07:42:33.096Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3ece406099f1250a5c9363f32c52f748e72ffe74", "commit_to": "f0eb2620393065dd299d7be69e3857bdd7e1eb29", "ref": "2.4.0", "commit_title": "fix otp channel bugs", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13129, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T07:29:23.739Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "406a9d05086ee36e24aea1dae0b2ff6edd3571fd", "commit_to": "370597a4a817ee66132cc49284e99734cabf7b25", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13128, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T07:29:10.142Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "847e20475e2c1941ef5f828fd564c1c7832b8222", "commit_to": "3ece406099f1250a5c9363f32c52f748e72ffe74", "ref": "2.4.0", "commit_title": "add status code http mapper", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13127, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T07:15:47.962Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "521d01d314b428b66f86887825f7e0968e04f801", "commit_to": "406a9d05086ee36e24aea1dae0b2ff6edd3571fd", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13126, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T07:15:32.847Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "df057e5b96d8a7d3066e261ae408fe8129938a72", "commit_to": "847e20475e2c1941ef5f828fd564c1c7832b8222", "ref": "2.4.0", "commit_title": "fix otp bugs", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13124, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T06:33:39.336Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "67a392676ec0e6a5a49565ab6ef580b9ed5acd84", "commit_to": "521d01d314b428b66f86887825f7e0968e04f801", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13123, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T06:33:13.970Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "2d783ec35284feb7357e8bbd5791ebbfb64a8728", "commit_to": "df057e5b96d8a7d3066e261ae408fe8129938a72", "ref": "2.4.0", "commit_title": "uncomment service test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13121, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T06:13:15.649Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "456d8660ef551ad3649b7ae8c76c387817f80616", "commit_to": "67a392676ec0e6a5a49565ab6ef580b9ed5acd84", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13120, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T06:12:22.918Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "227352a2a177d70d3cf2ad0dbed73cbba811c99c", "commit_to": "2d783ec35284feb7357e8bbd5791ebbfb64a8728", "ref": "2.4.0", "commit_title": "temp comment test case", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13119, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T06:02:47.385Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "3a5f0aeffb66176ebb0f24b00f709af9c7ce6f45", "commit_to": "456d8660ef551ad3649b7ae8c76c387817f80616", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13118, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T06:02:27.674Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "a9268a786a3eccb7fcd8461e2f621b7d3542b839", "commit_to": "227352a2a177d70d3cf2ad0dbed73cbba811c99c", "ref": "2.4.0", "commit_title": "fix test case", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13117, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T05:50:47.671Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "a644f7074343bc5ff84e608ad22c41fb03d2db44", "commit_to": "3a5f0aeffb66176ebb0f24b00f709af9c7ce6f45", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13116, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T05:50:24.020Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "6ea53b24199e8294c5f3869a5e1a86fe2c52127a", "commit_to": "a9268a786a3eccb7fcd8461e2f621b7d3542b839", "ref": "2.4.0", "commit_title": "temp comment subscriber request service test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13115, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T05:28:52.385Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "bf65eb96177fbb744fa50040fc6f21d1d0a52a1f", "commit_to": "6ea53b24199e8294c5f3869a5e1a86fe2c52127a", "ref": "2.4.0", "commit_title": "fix test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13114, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T05:28:46.459Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "aec5fd5730c90f95ac47bbf96cabe792b14fa6f0", "commit_to": "a644f7074343bc5ff84e608ad22c41fb03d2db44", "ref": "develop", "commit_title": "Merge branch '2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13113, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T05:27:20.253Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d95bec57cede39969d08649344adf8e9611e7536", "commit_to": "aec5fd5730c90f95ac47bbf96cabe792b14fa6f0", "ref": "develop", "commit_title": "fix test case", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13111, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T04:18:13.263Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "552d77059bf175b6125413c0723820da0f2e1a6b", "commit_to": "d95bec57cede39969d08649344adf8e9611e7536", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 13110, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-07T04:17:43.004Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "e7526bad22aa3188012943d69d102aa0950d7fbb", "commit_to": "bf65eb96177fbb744fa50040fc6f21d1d0a52a1f", "ref": "2.4.0", "commit_title": "Merge branch '2.4.0' of *************:carina/carina-rest into 2.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12818, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-10-01T03:44:03.328Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "6a946b7bfd16e69e54ae22ad30a6425961c22ea5", "commit_to": "679a8926beaafe00f8f74767c6d3329670096128", "ref": "develop", "commit_title": "Merge branch 'develop' of *************:carina/carina-rest into dev...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12283, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T07:20:25.443Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "633f337f008d1f85f8e8d1b31911de16866ab6a0", "commit_to": "3c3ad66e603904fb4d378b72cd179915db350b1a", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12282, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T07:19:58.199Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "ba9cf49943cb338590d9c6ea754b580eace65c03", "commit_to": "5ab3c820b1524b4080e2179e780e49990e93ada1", "ref": "2.4.0", "commit_title": "fix card statement audit", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12261, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T06:22:46.005Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "1da3c19a52d51c895c3864a0754d7ca9c01902aa", "commit_to": "633f337f008d1f85f8e8d1b31911de16866ab6a0", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12260, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T06:22:27.090Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "6e0234dad6eff6182c8f5ecad58bfcf3c3e8c5c2", "commit_to": "ba9cf49943cb338590d9c6ea754b580eace65c03", "ref": "2.4.0", "commit_title": "fix audit catch execption", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12259, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T05:56:04.251Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "73ee2b6e00b0d21cb1dbd9ce2bf89f934d0d8650", "commit_to": "1da3c19a52d51c895c3864a0754d7ca9c01902aa", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12258, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T05:55:37.512Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "f0058b7728fbbff585813103fc4c812046575ef5", "commit_to": "6e0234dad6eff6182c8f5ecad58bfcf3c3e8c5c2", "ref": "2.4.0", "commit_title": "Merge branch '2.4.0' of *************:carina/carina-rest into 2.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12247, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T04:00:30.567Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "118ba67529d416f36ad88198d770c7eca0aaa02a", "commit_to": "90e0c1089bb254542650e24721a3ea784e5bd741", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12246, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T04:00:08.388Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d2f6ed50f0bde804e54143468a56417b714a5032", "commit_to": "56301861808f856f021594e7c4120dadb8210e86", "ref": "2.4.0", "commit_title": "fix card statement audit aspect", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12245, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T03:34:14.418Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "b068707b761c554baab00c8ffc66de93a72fb2de", "commit_to": "118ba67529d416f36ad88198d770c7eca0aaa02a", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12244, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T03:33:49.955Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e5d8336f50a319cab6692aac764c4512b1753a0a", "commit_to": "d2f6ed50f0bde804e54143468a56417b714a5032", "ref": "2.4.0", "commit_title": "fix aspect error handling", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12239, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T02:50:04.785Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "9d8bc8fc6c2f2a83a10904f432416b9036d56c40", "commit_to": "b068707b761c554baab00c8ffc66de93a72fb2de", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12238, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T02:49:50.037Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b0ee9e510c329c7d44891638724f020f927bcddf", "commit_to": "e5d8336f50a319cab6692aac764c4512b1753a0a", "ref": "2.4.0", "commit_title": "minor fix", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12237, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T02:43:35.943Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "743354e6bc4b12ca16eb1be2f972e3ced97b08b6", "commit_to": "9d8bc8fc6c2f2a83a10904f432416b9036d56c40", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12236, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T02:43:14.163Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "b0a330f430a95981bc083b8b5894297adb776dde", "commit_to": "b0ee9e510c329c7d44891638724f020f927bcddf", "ref": "2.4.0", "commit_title": "fix card statement aspect", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12235, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T02:23:08.543Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 4, "action": "pushed", "ref_type": "branch", "commit_from": "62a5193fc5dcf66e059b31c8beab6f95135a744c", "commit_to": "743354e6bc4b12ca16eb1be2f972e3ced97b08b6", "ref": "develop", "commit_title": "Merge branch 'develop' of *************:carina/carina-rest into dev...", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12234, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-20T02:22:32.863Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "453d8aef22d8a6f7685f17827ad41fba4ca7e5aa", "commit_to": "b0a330f430a95981bc083b8b5894297adb776dde", "ref": "2.4.0", "commit_title": "Merge branch '2.4.0' of *************:carina/carina-rest into 2.4.0", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12142, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-19T01:32:00.891Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "42f7f52de46d926ff5755c12b6ead65450b8072c", "commit_to": "30c17024a90f843bb385efe6a5eb8155cab60ca5", "ref": "2.4.0", "commit_title": "minor fix, remove static", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12100, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-18T06:38:00.237Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "aead7fdde5252acb523ae484f03a87c4ebd27934", "commit_to": "46a3bbc387c81f6077b66f5194da560d1ee5b658", "ref": "2.4.0", "commit_title": "add func to print cms fields while testing", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 12079, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-18T03:59:42.225Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e6bb734fe9c761176b79207c17ca2c13ef0ef75c", "commit_to": "f7d8129c778a07ea22778b1e2963c513b2e28b0d", "ref": "2.4.0", "commit_title": "update cms test case", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 11686, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-09T03:25:17.998Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "21b2eada8e496f515710432d90efdd3a157d9491", "commit_to": "a60ade8e5d6a522fa97b849b9057a2d1471113ae", "ref": "2.4.0", "commit_title": "remove vts map util 6.4 sdk", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 11363, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-03T07:57:12.506Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "a3cdd70d65bee67d66c2422eb077a4fef1a230ee", "commit_to": "7a6e23eb3bc6f9bc8a23b18491e1fcddfd29f364", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 11362, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-09-03T07:55:37.516Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9dac17900a1c6b3bd21e8d4f7dba7b6ed0165065", "commit_to": "f80933b56beaf44d037c77204508e367104fc9b1", "ref": "2.4.0", "commit_title": "update sdk", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10849, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-26T23:52:09.431Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 10, "action": "pushed", "ref_type": "branch", "commit_from": "ec5e26bf8fd872628faebbd9f11c08d0b8802c96", "commit_to": "e8cd594a08c77633356b35dba7e01e82c96cca93", "ref": "develop", "commit_title": "merge uat temp fix, solve conflict", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10848, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-26T23:49:37.565Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 28, "action": "pushed", "ref_type": "branch", "commit_from": "808fc99f8b451c9be19ad5500e4724942a953025", "commit_to": "ec5e26bf8fd872628faebbd9f11c08d0b8802c96", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/2.4.0' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10817, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-26T07:27:31.247Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9faa2acbc40a7e044199c6fd33783d37e46eb79c", "commit_to": "9052e0c80befa689d793c98d3b39cee09272413b", "ref": "2.4.0", "commit_title": "minor fix", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10816, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-26T07:25:08.165Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "2e7b15788346a07c59f072a5c03d3efeab4aaeba", "commit_to": "9faa2acbc40a7e044199c6fd33783d37e46eb79c", "ref": "2.4.0", "commit_title": "add audit aspect and report", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10773, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-23T08:27:49.254Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "41c7a108af19c2ba182845ab47d16a2ba5e1d795", "commit_to": "cd17b5305d08f500aa200fd1f922c6e3250dc581", "ref": "2.4.0", "commit_title": "minor fix", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10636, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-21T03:28:39.663Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "75a41ced8fd3b19c24209588364863c0a34d884f", "commit_to": "c663f552e9f1a6d24bae74b699c89008d9b7f00a", "ref": "uat", "commit_title": "update logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10610, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-20T07:46:54.367Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "9781c77f685d86f9b056dedf8a670a5f5bcbd2bf", "commit_to": "c96aded27d358a7cc1ab91e1d32ae52189be4c0b", "ref": "2.4.0", "commit_title": "add scheduler", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10596, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-20T05:25:21.718Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "00d42ac318c56682089d16903b2189f17fcfe8dd", "commit_to": "75a41ced8fd3b19c24209588364863c0a34d884f", "ref": "uat", "commit_title": "ignore test", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10595, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-20T05:24:34.966Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "802fdf31aebdaab7e5f7a6153bb8bdae01cafe9c", "commit_to": "00d42ac318c56682089d16903b2189f17fcfe8dd", "ref": "uat", "commit_title": "fix logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10529, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-19T03:15:19.123Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "commit_to": "802fdf31aebdaab7e5f7a6153bb8bdae01cafe9c", "ref": "uat", "commit_title": "update cms gateway advice", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10528, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-19T03:13:36.584Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "pushed", "ref_type": "branch", "commit_from": "e166a871474f2f755eaed1944f2b65e77c702d38", "commit_to": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "ref": "uat", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10527, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-19T03:13:00.248Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 21, "action": "pushed", "ref_type": "branch", "commit_from": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "commit_to": "e166a871474f2f755eaed1944f2b65e77c702d38", "ref": "uat", "commit_title": "Bump to 2.3.2", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10526, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-19T03:12:56.054Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "pushed", "ref_type": "branch", "commit_from": "e166a871474f2f755eaed1944f2b65e77c702d38", "commit_to": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "ref": "uat", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10516, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-19T00:59:06.479Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "commit_to": "802fdf31aebdaab7e5f7a6153bb8bdae01cafe9c", "ref": "uat", "commit_title": "update cms gateway advice", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10515, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-19T00:54:30.862Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 276, "action": "pushed", "ref_type": "branch", "commit_from": "8d1f74ba69a5aefa78e798e93287ad3ba3b951e5", "commit_to": "802fdf31aebdaab7e5f7a6153bb8bdae01cafe9c", "ref": "uat-fix-branch", "commit_title": "update cms gateway advice", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10450, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-14T14:22:53.755Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 58, "action": "pushed", "ref_type": "branch", "commit_from": "59bf7433c3b22a94fe03db31762cd1349baee5da", "commit_to": "1db00c83271af01f67b25ff54cd5f2f3b3017397", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10449, "project_id": 306, "action_name": "accepted", "target_id": 694, "target_iid": 16, "target_type": "MergeRequest", "author_id": 11, "target_title": "2.3.0", "created_at": "2024-08-14T14:22:51.412Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 10444, "project_id": 305, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-14T14:00:32.024Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 16, "action": "pushed", "ref_type": "branch", "commit_from": "ef255228e23c81d3cb14d46a2f7496d4401f5503", "commit_to": "91e71a34c1e3945a025c03f78aed9d912958559f", "ref": "master", "commit_title": "Merge branch 'pre-master' into 'master'", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10443, "project_id": 305, "action_name": "accepted", "target_id": 693, "target_iid": 5, "target_type": "MergeRequest", "author_id": 11, "target_title": "2.3.0", "created_at": "2024-08-14T14:00:31.526Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "author_username": "<PERSON>"}, {"id": 10438, "project_id": 305, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-14T13:44:59.618Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "ef255228e23c81d3cb14d46a2f7496d4401f5503", "ref": "pre-master", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10405, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-14T07:52:58.904Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "59bf7433c3b22a94fe03db31762cd1349baee5da", "ref": "pre-master", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10389, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-14T07:20:35.855Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "pushed", "ref_type": "branch", "commit_from": "53e2812885d24bd924e780bd16c5d5e1f425fb84", "commit_to": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "ref": "uat", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10108, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-09T06:36:57.904Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 65, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "808fc99f8b451c9be19ad5500e4724942a953025", "ref": "cr64", "commit_title": "Merge remote-tracking branch 'origin/cr66' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 10001, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-08T04:13:16.083Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "92cbcf3ce24c6c272b746f306804f60f32c8f35f", "commit_to": "ac4a5939705c6e4afb7eb71924d835f851f86fdf", "ref": "cr65", "commit_title": "clean code", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9998, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-08T04:08:51.436Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "4e4589ae8afaf7ac63d55762226890cee94fcdbd", "commit_to": "92cbcf3ce24c6c272b746f306804f60f32c8f35f", "ref": "cr65", "commit_title": "add update email function", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9889, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-07T05:54:39.523Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 3, "action": "pushed", "ref_type": "branch", "commit_from": "970e8abdc0fd2c4a591db4991af2afbe63beb5b7", "commit_to": "4e4589ae8afaf7ac63d55762226890cee94fcdbd", "ref": "cr65", "commit_title": "added update email function, clear logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9786, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T07:49:13.465Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "d618fb5a3b8bf5406ac8c636696715b47d192706", "commit_to": "f63a641e3f4dc887c35e464fa4085751221ccbe1", "ref": "cr66", "commit_title": "remove logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9782, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T07:29:49.043Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 27, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "970e8abdc0fd2c4a591db4991af2afbe63beb5b7", "ref": "cr65", "commit_title": "combine limit urn, pan fix", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9781, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T07:26:51.658Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "42271d2a311cc8c604293147fb4155004e3800b3", "commit_to": "808fc99f8b451c9be19ad5500e4724942a953025", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/cr66' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9780, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T07:26:29.966Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "4c2eac0ce4b25698ee9619c5b85bf0c58f5bf40d", "commit_to": "d618fb5a3b8bf5406ac8c636696715b47d192706", "ref": "cr66", "commit_title": "fix injection", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9777, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T07:09:13.401Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 4, "action": "pushed", "ref_type": "branch", "commit_from": "80d5d81a1031febec6a1c61a8f476b31ea74eec7", "commit_to": "42271d2a311cc8c604293147fb4155004e3800b3", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/cr66' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9776, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T07:08:53.460Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "c271132506e7a6aaf442634b8862dc33769f063c", "commit_to": "4c2eac0ce4b25698ee9619c5b85bf0c58f5bf40d", "ref": "cr66", "commit_title": "add logger", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9768, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T06:36:43.120Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "2b14d56d151b5df6f30baa3f954ebae6e1d4b24a", "commit_to": "c271132506e7a6aaf442634b8862dc33769f063c", "ref": "cr66", "commit_title": "revert changes, update controller", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9766, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T06:33:43.728Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "4b969a98be8d35161c56b6f14bfe3b5962106c82", "commit_to": "80d5d81a1031febec6a1c61a8f476b31ea74eec7", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/cr66' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9765, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T06:33:26.938Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "371df86fc251caae8ac2e2522067459f8ded77e3", "commit_to": "2b14d56d151b5df6f30baa3f954ebae6e1d4b24a", "ref": "cr66", "commit_title": "fix controller", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9754, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T06:09:06.709Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "371df86fc251caae8ac2e2522067459f8ded77e3", "commit_to": "4b969a98be8d35161c56b6f14bfe3b5962106c82", "ref": "develop", "commit_title": "update queue group id", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9750, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T04:33:23.050Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0c8f3df94494f3d3e7c541fbc41cd64935ef8a30", "commit_to": "6f06b43f987b4724c134298903e4074927d38f04", "ref": "uat", "commit_title": "update queue group id", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9702, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T02:06:55.795Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "97b5c50fa94fecc623290dba9c12f0d1d3b4b057", "commit_to": "371df86fc251caae8ac2e2522067459f8ded77e3", "ref": "develop", "commit_title": "register db", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9701, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T02:06:38.249Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "97b5c50fa94fecc623290dba9c12f0d1d3b4b057", "commit_to": "371df86fc251caae8ac2e2522067459f8ded77e3", "ref": "cr66", "commit_title": "register db", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9700, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T01:52:53.902Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0b3b28c730b6be3249c132251a1aebb9e1ca3cbd", "commit_to": "97b5c50fa94fecc623290dba9c12f0d1d3b4b057", "ref": "develop", "commit_title": "fix controller injection", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9699, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T01:52:02.568Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "0b3b28c730b6be3249c132251a1aebb9e1ca3cbd", "commit_to": "97b5c50fa94fecc623290dba9c12f0d1d3b4b057", "ref": "cr66", "commit_title": "fix controller injection", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9698, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T01:44:13.970Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f754368c29fee218a183bf7cf71cdb500696e30b", "commit_to": "0b3b28c730b6be3249c132251a1aebb9e1ca3cbd", "ref": "develop", "commit_title": "fix security xml", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9697, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T01:44:05.613Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "f754368c29fee218a183bf7cf71cdb500696e30b", "commit_to": "0b3b28c730b6be3249c132251a1aebb9e1ca3cbd", "ref": "cr66", "commit_title": "fix security xml", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9694, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T01:26:57.052Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3d48e66a02566cc50038f32368f253a476edd894", "commit_to": "f754368c29fee218a183bf7cf71cdb500696e30b", "ref": "develop", "commit_title": "added upload and download profile pic", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9693, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-06T01:26:21.510Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "3d48e66a02566cc50038f32368f253a476edd894", "commit_to": "f754368c29fee218a183bf7cf71cdb500696e30b", "ref": "cr66", "commit_title": "added upload and download profile pic", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9679, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-05T08:55:37.997Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "a3ba19afb8b80a961afe4017eca169f9258ce03e", "commit_to": "13cb892cf6902640be6e87ba63d6bfe7f5ff6b72", "ref": "2.3.0-prepaid-statemement-temp-scheduler", "commit_title": "temp commit, 90% done", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9678, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-05T08:54:48.088Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 52, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "3d48e66a02566cc50038f32368f253a476edd894", "ref": "cr66", "commit_title": "Merge remote-tracking branch 'origin/combine-Limit' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9657, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-08-05T06:41:04.402Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 50, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "a3ba19afb8b80a961afe4017eca169f9258ce03e", "ref": "2.3.0-prepaid-statemement-temp-scheduler", "commit_title": "update http socket factory", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9229, "project_id": 306, "action_name": "pushed new", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-07-31T01:39:42.224Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 50, "action": "created", "ref_type": "branch", "commit_from": null, "commit_to": "a3ba19afb8b80a961afe4017eca169f9258ce03e", "ref": "2.3.0-prepaid-statemement", "commit_title": "update http socket factory", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9228, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-07-31T01:38:27.774Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "32c79164658d124ba12a4077af00eb85866f8561", "commit_to": "ca8506513e84c3b8fdf5c385cc155936749f083d", "ref": "uat", "commit_title": "Merge remote-tracking branch 'origin/develop' into uat", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 9227, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-07-31T01:37:39.073Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "09eca3d28de37e5c68fa40851711a4e8ecf26626", "commit_to": "a3ba19afb8b80a961afe4017eca169f9258ce03e", "ref": "develop", "commit_title": "update http socket factory", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 8938, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-07-25T02:47:30.722Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 2, "action": "pushed", "ref_type": "branch", "commit_from": "efabc701333b0c584d0e0739da092c5dd6c266bc", "commit_to": "4f7ccfb58ad82b5c5492fe488be96716c4b3cbf2", "ref": "develop", "commit_title": "Merge remote-tracking branch 'origin/combine-limit' into develop", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 8934, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-07-25T02:43:52.955Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 1, "action": "pushed", "ref_type": "branch", "commit_from": "e87eaa79d95005312bc7bebe8d0cf8d6211a349a", "commit_to": "a3f7da0ee86d1b4a53a87619237f45cfe4ee303d", "ref": "combine-limit", "commit_title": "update poller", "ref_count": null}, "author_username": "<PERSON>"}, {"id": 8923, "project_id": 306, "action_name": "pushed to", "target_id": null, "target_iid": null, "target_type": null, "author_id": 11, "target_title": null, "created_at": "2024-07-25T01:08:14.817Z", "author": {"id": 11, "username": "<PERSON>", "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://www.gravatar.com/avatar/91d99a80ba5514a32eb94ef3994efff366a72889aa0c30a044cd1b975c636e01?s=80&d=identicon", "web_url": "http://*************/Johnson"}, "imported": false, "imported_from": "none", "push_data": {"commit_count": 0, "action": "pushed", "ref_type": "branch", "commit_from": "0d961bd8b617c7de79cc7681e712a4023a1ecc0e", "commit_to": "efabc701333b0c584d0e0739da092c5dd6c266bc", "ref": "develop", "commit_title": null, "ref_count": null}, "author_username": "<PERSON>"}]