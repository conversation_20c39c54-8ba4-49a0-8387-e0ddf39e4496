[{"date": "2024-07-01", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2024-07-01T00:57:00.000Z", "end": "2024-07-01T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-07-01 08:57:00", "end": "2024-07-01 18:03:00"}}, {"date": "2024-07-02", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2024-07-02T01:00:00.000Z", "end": "2024-07-02T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-07-02 09:00:00", "end": "2024-07-02 18:01:00"}}, {"date": "2024-07-03", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2024-07-03T00:55:00.000Z", "end": "2024-07-03T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-07-03 08:55:00", "end": "2024-07-03 18:01:00"}}, {"date": "2024-07-04", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-07-04T00:56:00.000Z", "end": "2024-07-04T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-07-04 08:56:00", "end": "2024-07-04 18:02:00"}}, {"date": "2024-07-05", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-07-05T00:57:00.000Z", "end": "2024-07-05T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-07-05 08:57:00", "end": "2024-07-05 18:00:00"}}, {"date": "2024-08-02", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-02T00:55:00.000Z", "end": "2024-08-02T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-02 08:55:00", "end": "2024-08-02 18:02:00"}}, {"date": "2024-08-05", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-05T01:00:00.000Z", "end": "2024-08-05T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-05 09:00:00", "end": "2024-08-05 18:02:00"}}, {"date": "2024-08-06", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-06T00:55:00.000Z", "end": "2024-08-06T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-06 08:55:00", "end": "2024-08-06 18:00:00"}}, {"date": "2024-08-07", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-07T00:58:00.000Z", "end": "2024-08-07T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-07 08:58:00", "end": "2024-08-07 18:02:00"}}, {"date": "2024-08-08", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-08T00:59:00.000Z", "end": "2024-08-08T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-08 08:59:00", "end": "2024-08-08 18:02:00"}}, {"date": "2024-08-09", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-09T00:55:00.000Z", "end": "2024-08-09T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-09 08:55:00", "end": "2024-08-09 18:03:00"}}, {"date": "2024-08-12", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-12T00:55:00.000Z", "end": "2024-08-12T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-12 08:55:00", "end": "2024-08-12 18:02:00"}}, {"date": "2024-08-13", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-13T00:57:00.000Z", "end": "2024-08-13T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-13 08:57:00", "end": "2024-08-13 18:02:00"}}, {"date": "2024-08-14", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-14T00:55:00.000Z", "end": "2024-08-14T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-14 08:55:00", "end": "2024-08-14 18:04:00"}}, {"date": "2024-08-15", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-15T00:55:00.000Z", "end": "2024-08-15T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-15 08:55:00", "end": "2024-08-15 18:04:00"}}, {"date": "2024-08-16", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-16T00:59:00.000Z", "end": "2024-08-16T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-16 08:59:00", "end": "2024-08-16 18:05:00"}}, {"date": "2024-08-19", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-19T01:00:00.000Z", "end": "2024-08-19T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-19 09:00:00", "end": "2024-08-19 18:03:00"}}, {"date": "2024-08-20", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-20T00:59:00.000Z", "end": "2024-08-20T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-20 08:59:00", "end": "2024-08-20 18:05:00"}}, {"date": "2024-08-21", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-21T00:57:00.000Z", "end": "2024-08-21T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-21 08:57:00", "end": "2024-08-21 18:02:00"}}, {"date": "2024-08-22", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-22T00:56:00.000Z", "end": "2024-08-22T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-22 08:56:00", "end": "2024-08-22 18:05:00"}}, {"date": "2024-08-23", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-23T00:55:00.000Z", "end": "2024-08-23T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-23 08:55:00", "end": "2024-08-23 18:00:00"}}, {"date": "2024-08-26", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-26T00:55:00.000Z", "end": "2024-08-26T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-26 08:55:00", "end": "2024-08-26 18:02:00"}}, {"date": "2024-08-27", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-27T00:58:00.000Z", "end": "2024-08-27T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-27 08:58:00", "end": "2024-08-27 18:04:00"}}, {"date": "2024-08-28", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-28T00:56:00.000Z", "end": "2024-08-28T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-28 08:56:00", "end": "2024-08-28 18:05:00"}}, {"date": "2024-08-29", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-29T01:00:00.000Z", "end": "2024-08-29T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-29 09:00:00", "end": "2024-08-29 18:05:00"}}, {"date": "2024-08-30", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-08-30T01:00:00.000Z", "end": "2024-08-30T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-08-30 09:00:00", "end": "2024-08-30 18:01:00"}}, {"date": "2024-09-02", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-09-02T01:00:00.000Z", "end": "2024-09-02T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-09-02 09:00:00", "end": "2024-09-02 18:04:00"}}, {"date": "2024-09-03", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-09-03T00:55:00.000Z", "end": "2024-09-03T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-09-03 08:55:00", "end": "2024-09-03 18:03:00"}}, {"date": "2024-09-04", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-09-04T00:57:00.000Z", "end": "2024-09-04T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-09-04 08:57:00", "end": "2024-09-04 18:03:00"}}, {"date": "2024-09-05", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-09-05T00:58:00.000Z", "end": "2024-09-05T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-09-05 08:58:00", "end": "2024-09-05 18:04:00"}}, {"date": "2024-09-06", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-09-06T00:55:00.000Z", "end": "2024-09-06T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-09-06 08:55:00", "end": "2024-09-06 18:04:00"}}, {"date": "2024-09-17", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-09-17T00:59:00.000Z", "end": "2024-09-17T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-09-17 08:59:00", "end": "2024-09-17 18:01:00"}}, {"date": "2024-10-03", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-10-03T00:55:00.000Z", "end": "2024-10-03T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-10-03 08:55:00", "end": "2024-10-03 18:03:00"}}, {"date": "2024-10-29", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-10-29T00:59:00.000Z", "end": "2024-10-29T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-10-29 08:59:00", "end": "2024-10-29 18:04:00"}}, {"date": "2024-11-01", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-11-01T00:59:00.000Z", "end": "2024-11-01T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-11-01 08:59:00", "end": "2024-11-01 18:00:00"}}, {"date": "2024-11-15", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-11-15T01:00:00.000Z", "end": "2024-11-15T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-11-15 09:00:00", "end": "2024-11-15 18:04:00"}}, {"date": "2024-11-28", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-11-28T00:56:00.000Z", "end": "2024-11-28T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-11-28 08:56:00", "end": "2024-11-28 18:05:00"}}, {"date": "2024-11-29", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-11-29T00:56:00.000Z", "end": "2024-11-29T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-11-29 08:56:00", "end": "2024-11-29 18:04:00"}}, {"date": "2024-12-02", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-02T00:59:00.000Z", "end": "2024-12-02T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-02 08:59:00", "end": "2024-12-02 18:03:00"}}, {"date": "2024-12-03", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-03T00:57:00.000Z", "end": "2024-12-03T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-03 08:57:00", "end": "2024-12-03 18:01:00"}}, {"date": "2024-12-04", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-04T00:59:00.000Z", "end": "2024-12-04T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-04 08:59:00", "end": "2024-12-04 18:05:00"}}, {"date": "2024-12-05", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-05T00:59:00.000Z", "end": "2024-12-05T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-05 08:59:00", "end": "2024-12-05 18:05:00"}}, {"date": "2024-12-06", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-06T01:00:00.000Z", "end": "2024-12-06T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-06 09:00:00", "end": "2024-12-06 18:05:00"}}, {"date": "2024-12-09", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-09T00:59:00.000Z", "end": "2024-12-09T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-09 08:59:00", "end": "2024-12-09 18:02:00"}}, {"date": "2024-12-10", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-10T00:56:00.000Z", "end": "2024-12-10T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-10 08:56:00", "end": "2024-12-10 18:02:00"}}, {"date": "2024-12-12", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-12T00:55:00.000Z", "end": "2024-12-12T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-12 08:55:00", "end": "2024-12-12 18:04:00"}}, {"date": "2024-12-13", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-13T00:58:00.000Z", "end": "2024-12-13T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-13 08:58:00", "end": "2024-12-13 18:03:00"}}, {"date": "2024-12-16", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-16T00:55:00.000Z", "end": "2024-12-16T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-16 08:55:00", "end": "2024-12-16 18:00:00"}}, {"date": "2024-12-17", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-17T00:59:00.000Z", "end": "2024-12-17T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-17 08:59:00", "end": "2024-12-17 18:04:00"}}, {"date": "2024-12-18", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-18T00:55:00.000Z", "end": "2024-12-18T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-18 08:55:00", "end": "2024-12-18 18:03:00"}}, {"date": "2024-12-19", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-19T01:00:00.000Z", "end": "2024-12-19T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-19 09:00:00", "end": "2024-12-19 18:03:00"}}, {"date": "2024-12-20", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2024-12-20T00:57:00.000Z", "end": "2024-12-20T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2024-12-20 08:57:00", "end": "2024-12-20 18:03:00"}}, {"date": "2025-01-02", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-02T00:58:00.000Z", "end": "2025-01-02T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-02 08:58:00", "end": "2025-01-02 18:05:00"}}, {"date": "2025-01-03", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-03T01:00:00.000Z", "end": "2025-01-03T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-03 09:00:00", "end": "2025-01-03 18:03:00"}}, {"date": "2025-01-06", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-06T00:59:00.000Z", "end": "2025-01-06T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-06 08:59:00", "end": "2025-01-06 18:02:00"}}, {"date": "2025-01-07", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-07T00:56:00.000Z", "end": "2025-01-07T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-07 08:56:00", "end": "2025-01-07 18:03:00"}}, {"date": "2025-01-08", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-08T01:00:00.000Z", "end": "2025-01-08T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-08 09:00:00", "end": "2025-01-08 18:03:00"}}, {"date": "2025-01-09", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-09T00:59:00.000Z", "end": "2025-01-09T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-09 08:59:00", "end": "2025-01-09 18:01:00"}}, {"date": "2025-01-10", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-10T00:59:00.000Z", "end": "2025-01-10T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-10 08:59:00", "end": "2025-01-10 18:00:00"}}, {"date": "2025-01-13", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-13T00:56:00.000Z", "end": "2025-01-13T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-13 08:56:00", "end": "2025-01-13 18:01:00"}}, {"date": "2025-01-14", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-14T00:56:00.000Z", "end": "2025-01-14T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-14 08:56:00", "end": "2025-01-14 18:03:00"}}, {"date": "2025-01-15", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-15T00:59:00.000Z", "end": "2025-01-15T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-15 08:59:00", "end": "2025-01-15 18:01:00"}}, {"date": "2025-01-16", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-16T00:55:00.000Z", "end": "2025-01-16T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-16 08:55:00", "end": "2025-01-16 18:03:00"}}, {"date": "2025-01-17", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-17T00:59:00.000Z", "end": "2025-01-17T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-17 08:59:00", "end": "2025-01-17 18:04:00"}}, {"date": "2025-01-20", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-01-20T00:59:00.000Z", "end": "2025-01-20T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-01-20 08:59:00", "end": "2025-01-20 18:02:00"}}, {"date": "2025-02-10", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-02-10T00:58:00.000Z", "end": "2025-02-10T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-10 08:58:00", "end": "2025-02-10 18:00:00"}}, {"date": "2025-02-12", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-02-12T00:57:00.000Z", "end": "2025-02-12T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-12 08:57:00", "end": "2025-02-12 18:00:00"}}, {"date": "2025-02-13", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-02-13T00:58:00.000Z", "end": "2025-02-13T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-13 08:58:00", "end": "2025-02-13 18:03:00"}}, {"date": "2025-02-14", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-02-14T00:56:00.000Z", "end": "2025-02-14T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-14 08:56:00", "end": "2025-02-14 18:02:00"}}, {"date": "2025-02-17", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-02-17T00:58:00.000Z", "end": "2025-02-17T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-17 08:58:00", "end": "2025-02-17 18:05:00"}}, {"date": "2025-02-18", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-18T00:56:00.000Z", "end": "2025-02-18T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-18 08:56:00", "end": "2025-02-18 18:03:00"}}, {"date": "2025-02-19", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-19T00:58:00.000Z", "end": "2025-02-19T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-19 08:58:00", "end": "2025-02-19 18:03:00"}}, {"date": "2025-02-20", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-20T00:55:00.000Z", "end": "2025-02-20T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-20 08:55:00", "end": "2025-02-20 18:03:00"}}, {"date": "2025-02-21", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-21T00:58:00.000Z", "end": "2025-02-21T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-21 08:58:00", "end": "2025-02-21 18:04:00"}}, {"date": "2025-02-24", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-24T00:55:00.000Z", "end": "2025-02-24T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-24 08:55:00", "end": "2025-02-24 18:04:00"}}, {"date": "2025-02-25", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-25T00:59:00.000Z", "end": "2025-02-25T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-25 08:59:00", "end": "2025-02-25 18:03:00"}}, {"date": "2025-02-26", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-26T00:57:00.000Z", "end": "2025-02-26T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-26 08:57:00", "end": "2025-02-26 18:04:00"}}, {"date": "2025-02-27", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-02-27T00:58:00.000Z", "end": "2025-02-27T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-27 08:58:00", "end": "2025-02-27 18:04:00"}}, {"date": "2025-02-28", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-02-28T00:55:00.000Z", "end": "2025-02-28T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-02-28 08:55:00", "end": "2025-02-28 18:00:00"}}, {"date": "2025-03-03", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-03T00:58:00.000Z", "end": "2025-03-03T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-03 08:58:00", "end": "2025-03-03 18:02:00"}}, {"date": "2025-03-04", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-04T00:57:00.000Z", "end": "2025-03-04T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-04 08:57:00", "end": "2025-03-04 18:02:00"}}, {"date": "2025-03-05", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-05T00:55:00.000Z", "end": "2025-03-05T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-05 08:55:00", "end": "2025-03-05 18:05:00"}}, {"date": "2025-03-06", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-06T00:59:00.000Z", "end": "2025-03-06T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-06 08:59:00", "end": "2025-03-06 18:03:00"}}, {"date": "2025-03-07", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-07T00:55:00.000Z", "end": "2025-03-07T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-07 08:55:00", "end": "2025-03-07 18:02:00"}}, {"date": "2025-03-10", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-10T00:57:00.000Z", "end": "2025-03-10T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-10 08:57:00", "end": "2025-03-10 18:01:00"}}, {"date": "2025-03-11", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-11T00:58:00.000Z", "end": "2025-03-11T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-11 08:58:00", "end": "2025-03-11 18:01:00"}}, {"date": "2025-03-12", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-12T00:56:00.000Z", "end": "2025-03-12T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-12 08:56:00", "end": "2025-03-12 18:00:00"}}, {"date": "2025-03-13", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-13T00:56:00.000Z", "end": "2025-03-13T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-13 08:56:00", "end": "2025-03-13 18:02:00"}}, {"date": "2025-03-14", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-14T00:57:00.000Z", "end": "2025-03-14T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-14 08:57:00", "end": "2025-03-14 18:05:00"}}, {"date": "2025-03-17", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-17T01:00:00.000Z", "end": "2025-03-17T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-17 09:00:00", "end": "2025-03-17 18:05:00"}}, {"date": "2025-03-19", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-19T00:56:00.000Z", "end": "2025-03-19T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-19 08:56:00", "end": "2025-03-19 18:03:00"}}, {"date": "2025-03-20", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-20T00:58:00.000Z", "end": "2025-03-20T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-20 08:58:00", "end": "2025-03-20 18:03:00"}}, {"date": "2025-03-21", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-21T00:59:00.000Z", "end": "2025-03-21T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-21 08:59:00", "end": "2025-03-21 18:02:00"}}, {"date": "2025-03-24", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-24T01:00:00.000Z", "end": "2025-03-24T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-24 09:00:00", "end": "2025-03-24 18:03:00"}}, {"date": "2025-03-25", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-03-25T00:57:00.000Z", "end": "2025-03-25T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-25 08:57:00", "end": "2025-03-25 18:05:00"}}, {"date": "2025-03-26", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-03-26T00:57:00.000Z", "end": "2025-03-26T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-26 08:57:00", "end": "2025-03-26 18:02:00"}}, {"date": "2025-03-27", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-03-27T01:00:00.000Z", "end": "2025-03-27T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-27 09:00:00", "end": "2025-03-27 18:00:00"}}, {"date": "2025-03-28", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-03-28T00:55:00.000Z", "end": "2025-03-28T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-03-28 08:55:00", "end": "2025-03-28 18:04:00"}}, {"date": "2025-04-02", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-02T00:57:00.000Z", "end": "2025-04-02T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-02 08:57:00", "end": "2025-04-02 18:00:00"}}, {"date": "2025-04-03", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-03T00:55:00.000Z", "end": "2025-04-03T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-03 08:55:00", "end": "2025-04-03 18:01:00"}}, {"date": "2025-04-04", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-04T00:56:00.000Z", "end": "2025-04-04T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-04 08:56:00", "end": "2025-04-04 18:01:00"}}, {"date": "2025-04-07", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-07T00:59:00.000Z", "end": "2025-04-07T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-07 08:59:00", "end": "2025-04-07 18:03:00"}}, {"date": "2025-04-08", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-08T00:55:00.000Z", "end": "2025-04-08T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-08 08:55:00", "end": "2025-04-08 18:03:00"}}, {"date": "2025-04-09", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-09T00:57:00.000Z", "end": "2025-04-09T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-09 08:57:00", "end": "2025-04-09 18:03:00"}}, {"date": "2025-04-10", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-10T01:00:00.000Z", "end": "2025-04-10T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-10 09:00:00", "end": "2025-04-10 18:04:00"}}, {"date": "2025-04-11", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-11T00:59:00.000Z", "end": "2025-04-11T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-11 08:59:00", "end": "2025-04-11 18:01:00"}}, {"date": "2025-04-14", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-14T00:59:00.000Z", "end": "2025-04-14T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-14 08:59:00", "end": "2025-04-14 18:03:00"}}, {"date": "2025-04-15", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-15T00:58:00.000Z", "end": "2025-04-15T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-15 08:58:00", "end": "2025-04-15 18:01:00"}}, {"date": "2025-04-16", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-16T00:59:00.000Z", "end": "2025-04-16T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-16 08:59:00", "end": "2025-04-16 18:04:00"}}, {"date": "2025-04-17", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-17T00:57:00.000Z", "end": "2025-04-17T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-17 08:57:00", "end": "2025-04-17 18:01:00"}}, {"date": "2025-04-18", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-18T00:58:00.000Z", "end": "2025-04-18T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-18 08:58:00", "end": "2025-04-18 18:04:00"}}, {"date": "2025-04-21", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-04-21T00:58:00.000Z", "end": "2025-04-21T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-21 08:58:00", "end": "2025-04-21 18:04:00"}}, {"date": "2025-04-22", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-04-22T00:58:00.000Z", "end": "2025-04-22T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-22 08:58:00", "end": "2025-04-22 18:05:00"}}, {"date": "2025-04-23", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-23T01:00:00.000Z", "end": "2025-04-23T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-23 09:00:00", "end": "2025-04-23 18:05:00"}}, {"date": "2025-04-24", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-24T00:58:00.000Z", "end": "2025-04-24T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-24 08:58:00", "end": "2025-04-24 18:01:00"}}, {"date": "2025-04-25", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-25T00:57:00.000Z", "end": "2025-04-25T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-25 08:57:00", "end": "2025-04-25 18:05:00"}}, {"date": "2025-04-28", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-04-28T00:59:00.000Z", "end": "2025-04-28T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-28 08:59:00", "end": "2025-04-28 18:05:00"}}, {"date": "2025-04-29", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-04-29T01:00:00.000Z", "end": "2025-04-29T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-29 09:00:00", "end": "2025-04-29 18:01:00"}}, {"date": "2025-04-30", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-04-30T00:55:00.000Z", "end": "2025-04-30T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-04-30 08:55:00", "end": "2025-04-30 18:02:00"}}, {"date": "2025-05-02", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-05-02T00:56:00.000Z", "end": "2025-05-02T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-02 08:56:00", "end": "2025-05-02 18:04:00"}}, {"date": "2025-05-05", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-05-05T00:55:00.000Z", "end": "2025-05-05T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-05 08:55:00", "end": "2025-05-05 18:04:00"}}, {"date": "2025-05-06", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-06T00:58:00.000Z", "end": "2025-05-06T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-06 08:58:00", "end": "2025-05-06 18:00:00"}}, {"date": "2025-05-07", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-07T00:55:00.000Z", "end": "2025-05-07T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-07 08:55:00", "end": "2025-05-07 18:02:00"}}, {"date": "2025-05-08", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-08T00:56:00.000Z", "end": "2025-05-08T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-08 08:56:00", "end": "2025-05-08 18:05:00"}}, {"date": "2025-05-09", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-09T00:59:00.000Z", "end": "2025-05-09T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-09 08:59:00", "end": "2025-05-09 18:02:00"}}, {"date": "2025-05-13", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-05-13T00:59:00.000Z", "end": "2025-05-13T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-13 08:59:00", "end": "2025-05-13 18:05:00"}}, {"date": "2025-05-14", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-05-14T01:00:00.000Z", "end": "2025-05-14T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-14 09:00:00", "end": "2025-05-14 18:04:00"}}, {"date": "2025-05-15", "projectId": "65556736b51a0146381575b2", "projectName": "Internal Activity", "description": "Internal Activity development/support/meeting", "start": "2025-05-15T00:57:00.000Z", "end": "2025-05-15T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-15 08:57:00", "end": "2025-05-15 18:01:00"}}, {"date": "2025-05-16", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-05-16T00:56:00.000Z", "end": "2025-05-16T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-16 08:56:00", "end": "2025-05-16 18:04:00"}}, {"date": "2025-05-19", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-19T00:59:00.000Z", "end": "2025-05-19T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-19 08:59:00", "end": "2025-05-19 18:00:00"}}, {"date": "2025-05-20", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-20T00:58:00.000Z", "end": "2025-05-20T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-20 08:58:00", "end": "2025-05-20 18:05:00"}}, {"date": "2025-05-21", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-21T00:59:00.000Z", "end": "2025-05-21T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-21 08:59:00", "end": "2025-05-21 18:04:00"}}, {"date": "2025-05-22", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-22T00:56:00.000Z", "end": "2025-05-22T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-22 08:56:00", "end": "2025-05-22 18:04:00"}}, {"date": "2025-05-23", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-23T01:00:00.000Z", "end": "2025-05-23T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-23 09:00:00", "end": "2025-05-23 18:02:00"}}, {"date": "2025-05-26", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-26T00:57:00.000Z", "end": "2025-05-26T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-26 08:57:00", "end": "2025-05-26 18:01:00"}}, {"date": "2025-05-27", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-27T00:56:00.000Z", "end": "2025-05-27T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-27 08:56:00", "end": "2025-05-27 18:01:00"}}, {"date": "2025-05-28", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-28T00:59:00.000Z", "end": "2025-05-28T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-28 08:59:00", "end": "2025-05-28 18:00:00"}}, {"date": "2025-05-29", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-29T00:56:00.000Z", "end": "2025-05-29T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-29 08:56:00", "end": "2025-05-29 18:04:00"}}, {"date": "2025-05-30", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-05-30T00:58:00.000Z", "end": "2025-05-30T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-05-30 08:58:00", "end": "2025-05-30 18:00:00"}}, {"date": "2025-06-03", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-03T00:55:00.000Z", "end": "2025-06-03T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-03 08:55:00", "end": "2025-06-03 18:04:00"}}, {"date": "2025-06-04", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-04T00:58:00.000Z", "end": "2025-06-04T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-04 08:58:00", "end": "2025-06-04 18:05:00"}}, {"date": "2025-06-05", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-05T00:58:00.000Z", "end": "2025-06-05T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-05 08:58:00", "end": "2025-06-05 18:02:00"}}, {"date": "2025-06-06", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-06T01:00:00.000Z", "end": "2025-06-06T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-06 09:00:00", "end": "2025-06-06 18:03:00"}}, {"date": "2025-06-09", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-09T00:59:00.000Z", "end": "2025-06-09T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-09 08:59:00", "end": "2025-06-09 18:02:00"}}, {"date": "2025-06-10", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-10T01:00:00.000Z", "end": "2025-06-10T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-10 09:00:00", "end": "2025-06-10 18:04:00"}}, {"date": "2025-06-11", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-11T01:00:00.000Z", "end": "2025-06-11T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-11 09:00:00", "end": "2025-06-11 18:01:00"}}, {"date": "2025-06-12", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-12T00:55:00.000Z", "end": "2025-06-12T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-12 08:55:00", "end": "2025-06-12 18:03:00"}}, {"date": "2025-06-13", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-13T00:57:00.000Z", "end": "2025-06-13T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-13 08:57:00", "end": "2025-06-13 18:02:00"}}, {"date": "2025-06-16", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-16T01:00:00.000Z", "end": "2025-06-16T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-16 09:00:00", "end": "2025-06-16 18:03:00"}}, {"date": "2025-06-17", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-17T00:59:00.000Z", "end": "2025-06-17T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-17 08:59:00", "end": "2025-06-17 18:02:00"}}, {"date": "2025-06-18", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-18T00:58:00.000Z", "end": "2025-06-18T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-18 08:58:00", "end": "2025-06-18 18:02:00"}}, {"date": "2025-06-19", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-19T00:59:00.000Z", "end": "2025-06-19T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-19 08:59:00", "end": "2025-06-19 18:00:00"}}, {"date": "2025-06-20", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-20T00:58:00.000Z", "end": "2025-06-20T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-20 08:58:00", "end": "2025-06-20 18:02:00"}}, {"date": "2025-06-23", "projectId": "68809be7cb14c14f3fa667a7", "projectName": "nLicensing", "description": "nlicense development/support/meeting", "start": "2025-06-23T00:59:00.000Z", "end": "2025-06-23T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-23 08:59:00", "end": "2025-06-23 18:00:00"}}, {"date": "2025-06-24", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-24T00:57:00.000Z", "end": "2025-06-24T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-24 08:57:00", "end": "2025-06-24 18:03:00"}}, {"date": "2025-06-25", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-25T01:00:00.000Z", "end": "2025-06-25T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-25 09:00:00", "end": "2025-06-25 18:04:00"}}, {"date": "2025-06-26", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-26T00:58:00.000Z", "end": "2025-06-26T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-26 08:58:00", "end": "2025-06-26 18:04:00"}}, {"date": "2025-06-30", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-06-30T00:56:00.000Z", "end": "2025-06-30T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-06-30 08:56:00", "end": "2025-06-30 18:05:00"}}, {"date": "2025-07-01", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-01T00:57:00.000Z", "end": "2025-07-01T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-01 08:57:00", "end": "2025-07-01 18:05:00"}}, {"date": "2025-07-02", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-02T01:00:00.000Z", "end": "2025-07-02T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-02 09:00:00", "end": "2025-07-02 18:01:00"}}, {"date": "2025-07-03", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-03T00:56:00.000Z", "end": "2025-07-03T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-03 08:56:00", "end": "2025-07-03 18:01:00"}}, {"date": "2025-07-04", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-04T00:58:00.000Z", "end": "2025-07-04T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-04 08:58:00", "end": "2025-07-04 18:05:00"}}, {"date": "2025-07-07", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-07T00:55:00.000Z", "end": "2025-07-07T10:05:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-07 08:55:00", "end": "2025-07-07 18:05:00"}}, {"date": "2025-07-08", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-08T00:55:00.000Z", "end": "2025-07-08T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-08 08:55:00", "end": "2025-07-08 18:02:00"}}, {"date": "2025-07-09", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-09T00:56:00.000Z", "end": "2025-07-09T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-09 08:56:00", "end": "2025-07-09 18:00:00"}}, {"date": "2025-07-10", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-10T00:58:00.000Z", "end": "2025-07-10T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-10 08:58:00", "end": "2025-07-10 18:01:00"}}, {"date": "2025-07-11", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-11T00:59:00.000Z", "end": "2025-07-11T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-11 08:59:00", "end": "2025-07-11 18:00:00"}}, {"date": "2025-07-14", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-14T00:57:00.000Z", "end": "2025-07-14T10:03:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-14 08:57:00", "end": "2025-07-14 18:03:00"}}, {"date": "2025-07-15", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-15T00:58:00.000Z", "end": "2025-07-15T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-15 08:58:00", "end": "2025-07-15 18:00:00"}}, {"date": "2025-07-16", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-16T00:58:00.000Z", "end": "2025-07-16T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-16 08:58:00", "end": "2025-07-16 18:00:00"}}, {"date": "2025-07-17", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-17T00:57:00.000Z", "end": "2025-07-17T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-17 08:57:00", "end": "2025-07-17 18:00:00"}}, {"date": "2025-07-18", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-18T00:56:00.000Z", "end": "2025-07-18T10:04:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-18 08:56:00", "end": "2025-07-18 18:04:00"}}, {"date": "2025-07-21", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-21T00:58:00.000Z", "end": "2025-07-21T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-21 08:58:00", "end": "2025-07-21 18:02:00"}}, {"date": "2025-07-22", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-22T00:58:00.000Z", "end": "2025-07-22T10:02:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-22 08:58:00", "end": "2025-07-22 18:02:00"}}, {"date": "2025-07-23", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-23T00:58:00.000Z", "end": "2025-07-23T10:01:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-23 08:58:00", "end": "2025-07-23 18:01:00"}}, {"date": "2025-07-24", "projectId": "655444b7e7f1e3635bb5ba46", "projectName": "CB Bank (CB Card+)", "description": "Carina Rest development/support/meeting", "start": "2025-07-24T00:57:00.000Z", "end": "2025-07-24T10:00:00.000Z", "workspaceId": "6554437ee7f1e3635bb5aa22", "userId": "655b082e053d7164ae1c8054", "malaysiaTime": {"start": "2025-07-24 08:57:00", "end": "2025-07-24 18:00:00"}}]