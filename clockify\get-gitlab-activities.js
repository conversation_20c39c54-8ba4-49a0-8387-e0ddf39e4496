const fs = require('fs');
const path = require('path');
const http = require('http');

/**
 * Read GitLab API Key from file
 */
function readGitLabApiKey() {
    try {
        const apiKeyPath = path.join(__dirname, 'gitlab-apikey.txt');
        const apiKey = fs.readFileSync(apiKeyPath, 'utf8').trim();
        if (!apiKey) {
            throw new Error('GitLab API Key is empty');
        }
        return apiKey;
    } catch (error) {
        console.error('Failed to read GitLab API Key:', error.message);
        process.exit(1);
    }
}

/**
 * Send HTTP request to GitLab API
 */
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    reject(new Error('Failed to parse JSON response: ' + error.message));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error('Request failed: ' + error.message));
        });

        req.end();
    });
}

/**
 * Get current user info to get user ID
 */
async function getCurrentUser(apiKey) {
    const options = {
        hostname: '*************',
        port: 80,
        path: '/api/v4/user',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        console.log('Getting current user info...');
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            return response.data;
        } else {
            console.error(`Failed to get user info, status code: ${response.statusCode}`);
            console.error('Response:', response.data);
            return null;
        }

    } catch (error) {
        console.error('Failed to get current user:', error.message);
        return null;
    }
}

/**
 * Get user activities from July 1, 2024 to today
 */
async function getUserActivities(userId, apiKey) {
    // Set date range from July 1, 2024 to today
    const startDate = new Date('2024-07-01T00:00:00.000Z');
    const endDate = new Date(); // Today
    const afterDate = startDate.toISOString();
    const beforeDate = endDate.toISOString();

    const options = {
        hostname: '*************',
        port: 80,
        path: `/api/v4/users/${userId}/events?after=${afterDate}&before=${beforeDate}&per_page=100&sort=desc`,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        console.log(`Fetching activities for user ${userId} from ${afterDate} to ${beforeDate}...`);
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            return {
                activities: response.data,
                totalPages: parseInt(response.headers['x-total-pages'] || '1'),
                totalCount: parseInt(response.headers['x-total'] || '0'),
                currentPage: 1,
                dateRange: {
                    start: afterDate,
                    end: beforeDate
                }
            };
        } else {
            console.error(`Failed to get activities, status code: ${response.statusCode}`);
            console.error('Response:', response.data);
            return null;
        }

    } catch (error) {
        console.error('Failed to get user activities:', error.message);
        return null;
    }
}

/**
 * Get activities from a specific page
 */
async function getActivitiesPage(userId, apiKey, page, afterDate, beforeDate) {
    const options = {
        hostname: '*************',
        port: 80,
        path: `/api/v4/users/${userId}/events?after=${afterDate}&before=${beforeDate}&per_page=100&sort=desc&page=${page}`,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            return response.data;
        } else {
            console.error(`Failed to get activities page ${page}, status code: ${response.statusCode}`);
            return [];
        }

    } catch (error) {
        console.error(`Failed to get activities page ${page}:`, error.message);
        return [];
    }
}

/**
 * Get project information by project ID
 */
async function getProjectInfo(projectId, apiKey) {
    const options = {
        hostname: '*************',
        port: 80,
        path: `/api/v4/projects/${projectId}`,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            return {
                id: response.data.id,
                name: response.data.name,
                path: response.data.path,
                namespace: response.data.namespace?.name || '',
                fullPath: response.data.path_with_namespace || ''
            };
        } else {
            console.error(`Failed to get project ${projectId}, status code: ${response.statusCode}`);
            return null;
        }

    } catch (error) {
        console.error(`Failed to get project ${projectId}:`, error.message);
        return null;
    }
}

/**
 * Enrich activities with project information and simplify for Clockify
 */
async function enrichActivitiesWithProjects(activities, apiKey) {
    const projectCache = new Map();
    const simplifiedActivities = [];

    console.log('Enriching activities with project information...');

    for (let i = 0; i < activities.length; i++) {
        const activity = activities[i];

        if (activity.project_id && !projectCache.has(activity.project_id)) {
            console.log(`Fetching project info for ID: ${activity.project_id} (${i + 1}/${activities.length})`);
            const projectInfo = await getProjectInfo(activity.project_id, apiKey);
            if (projectInfo) {
                projectCache.set(activity.project_id, projectInfo);
            }
        }

        // Create simplified activity for Clockify
        const simplified = {
            date: activity.created_at.split('T')[0], // Just the date part
            time: activity.created_at.split('T')[1].split('.')[0], // Just the time part
            action: activity.action_name,
            description: generateDescription(activity, projectCache.get(activity.project_id)),
            project: projectCache.get(activity.project_id)?.name || 'Unknown Project',
            projectPath: projectCache.get(activity.project_id)?.fullPath || '',
        };

        simplifiedActivities.push(simplified);
    }

    console.log(`Created ${simplifiedActivities.length} simplified activities for Clockify`);
    return simplifiedActivities;
}

/**
 * Generate a meaningful description for Clockify time entry
 */
function generateDescription(activity, projectInfo) {
    const projectName = projectInfo?.name || 'Unknown Project';
    const action = activity.action_name;
    const target = activity.target_title || activity.target_type || '';

    switch (action) {
        case 'pushed':
            return `Code push to ${projectName}`;
        case 'created':
            if (activity.target_type === 'Issue') {
                return `Created issue: ${target} in ${projectName}`;
            } else if (activity.target_type === 'MergeRequest') {
                return `Created merge request: ${target} in ${projectName}`;
            }
            return `Created ${activity.target_type || 'item'} in ${projectName}`;
        case 'commented':
            return `Commented on ${activity.target_type || 'item'}: ${target} in ${projectName}`;
        case 'merged':
            return `Merged: ${target} in ${projectName}`;
        case 'closed':
            return `Closed ${activity.target_type || 'item'}: ${target} in ${projectName}`;
        case 'opened':
            return `Opened ${activity.target_type || 'item'}: ${target} in ${projectName}`;
        case 'updated':
            return `Updated ${activity.target_type || 'item'}: ${target} in ${projectName}`;
        default:
            return `${action} in ${projectName}${target ? ': ' + target : ''}`;
    }
}

/**
 * Print simplified activity information for Clockify
 */
function printActivity(activity, index) {
    console.log(`${index + 1}. ${activity.date} ${activity.time}`);
    console.log(`   Project: ${activity.project}`);
    console.log(`   Description: ${activity.description}`);
    console.log('');
}

/**
 * Group activities by date for easier Clockify entry
 */
function groupActivitiesByDate(activities) {
    const grouped = {};

    activities.forEach(activity => {
        const date = activity.date;
        if (!grouped[date]) {
            grouped[date] = [];
        }
        grouped[date].push(activity);
    });

    return grouped;
}

/**
 * Main function
 */
async function main() {
    console.log('GitLab Activity Fetcher (July 1, 2024 - Today)');
    console.log('='.repeat(50));

    try {
        const apiKey = readGitLabApiKey();

        // Get current user
        const user = await getCurrentUser(apiKey);
        if (!user) {
            console.error('Failed to get user information');
            process.exit(1);
        }

        console.log(`Current user: ${user.name} (@${user.username})`);
        console.log(`User ID: ${user.id}\n`);

        // Get activities
        const result = await getUserActivities(user.id, apiKey);
        if (!result) {
            console.error('Failed to get activities');
            process.exit(1);
        }

        console.log(`Found ${result.totalCount} activities from July 1, 2024 to today`);
        console.log(`Date range: ${result.dateRange.start} to ${result.dateRange.end}`);
        console.log(`Total pages: ${result.totalPages}\n`);

        let allActivities = [...result.activities];

        // Get remaining pages if there are more
        if (result.totalPages > 1) {
            for (let page = 2; page <= result.totalPages; page++) {
                console.log(`Fetching page ${page}/${result.totalPages}...`);
                const pageActivities = await getActivitiesPage(
                    user.id,
                    apiKey,
                    page,
                    result.dateRange.start,
                    result.dateRange.end
                );
                allActivities = allActivities.concat(pageActivities);
            }
        }

        console.log(`\nTotal activities fetched: ${allActivities.length}`);

        // Create simplified activities for Clockify
        const clockifyActivities = await enrichActivitiesWithProjects(allActivities, apiKey);

        console.log(`\nClockify Time Entries (${clockifyActivities.length} total):`);
        console.log('='.repeat(60));

        // Print simplified activities
        clockifyActivities.forEach((activity, index) => {
            printActivity(activity, index);
        });

        // Group activities by date for easier Clockify entry
        const groupedByDate = groupActivitiesByDate(clockifyActivities);

        console.log('\nGrouped by Date for Clockify:');
        console.log('='.repeat(40));
        Object.keys(groupedByDate).sort().forEach(date => {
            console.log(`\n📅 ${date}:`);
            groupedByDate[date].forEach((activity, index) => {
                console.log(`   ${index + 1}. [${activity.time}] ${activity.description}`);
            });
        });

        // Save simplified data for Clockify
        const clockifyFilePath = path.join(__dirname, 'clockify-time-entries.json');
        fs.writeFileSync(clockifyFilePath, JSON.stringify(clockifyActivities, null, 2));
        console.log(`\nClockify time entries saved to: ${clockifyFilePath}`);

        // Save grouped data
        const groupedFilePath = path.join(__dirname, 'clockify-grouped-by-date.json');
        fs.writeFileSync(groupedFilePath, JSON.stringify(groupedByDate, null, 2));
        console.log(`Grouped by date saved to: ${groupedFilePath}`);

    } catch (error) {
        console.error('Execution failed:', error.message);
        process.exit(1);
    }
}

// Run script
if (require.main === module) {
    main();
}

module.exports = {
    getUserActivities,
    getCurrentUser,
    readGitLabApiKey
};
