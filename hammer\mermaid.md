graph TB
subgraph "Client Layer"
MA[📱 Mobile App<br/>SDK + APP]
AP[🖥️ Admin Portal<br/>Angular]
end

    subgraph "Core Services"
        LS[⭐ Loyalty System<br/><br/>+ Tokenization SDK]
        ST[🆔 SToken CHID<br/>Backend]
    end

    subgraph "Infrastructure"
        K8S[☸️ Infrastructure<br/>K8s + CI/CD]
    end

    subgraph "Database Layer"
        DB[🗄️ Database<br/>MongoDB]
    end

    subgraph "N2TAP"
        N2T[📡 N2TAP<br/]
    end

    subgraph "External Integration"
        MS[🔌 Minesec API]
    end

    MA --> LS
    MA --> N2T
    AP --> LS
    ST <--> LS
    LS --> MS

    LS --> DB
    ST --> DB

    K8S -.-> LS
    K8S -.-> ST
    K8S -.-> AP

    style LS fill:#ff6b6b,stroke:#d63031,stroke-width:3px,color:#fff
    style MA fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    style AP fill:#a29bfe,stroke:#6c5ce7,stroke-width:2px,color:#fff
    style ST fill:#00b894,stroke:#00a085,stroke-width:2px,color:#fff
    style DB fill:#fdcb6e,stroke:#e17055,stroke-width:2px,color:#fff
    style N2T fill:#e84393,stroke:#d63031,stroke-width:2px,color:#fff
