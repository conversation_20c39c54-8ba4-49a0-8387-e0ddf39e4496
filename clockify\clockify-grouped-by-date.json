{"2025-07-21": [{"date": "2025-07-21", "time": "03:32:13", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-07-21", "time": "03:32:13", "action": "accepted", "description": "accepted in carina-portal: Premaster CR0064, CR0075, CR0076", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-07-21", "time": "03:32:10", "action": "approved", "description": "approved in carina-portal: Premaster CR0064, CR0075, CR0076", "project": "carina-portal", "projectPath": "carina/carina-portal"}], "2025-07-17": [{"date": "2025-07-17", "time": "02:37:58", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-17", "time": "02:37:11", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-17", "time": "02:36:36", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-17", "time": "02:36:04", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-17", "time": "02:27:02", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-17", "time": "02:25:42", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-17", "time": "02:00:20", "action": "pushed new", "description": "pushed new in hammer-endpoint-loyalty", "project": "hammer-endpoint-loyalty", "projectPath": "hammer/hammer-endpoint-loyalty"}, {"date": "2025-07-17", "time": "02:00:15", "action": "created", "description": "Created item in hammer-endpoint-loyalty", "project": "hammer-endpoint-loyalty", "projectPath": "hammer/hammer-endpoint-loyalty"}], "2025-07-16": [{"date": "2025-07-16", "time": "08:31:13", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-07-14": [{"date": "2025-07-14", "time": "06:44:19", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-14", "time": "06:38:01", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-07-03": [{"date": "2025-07-03", "time": "02:43:17", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-07-03", "time": "01:30:01", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-06-30": [{"date": "2025-06-30", "time": "01:40:17", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-06-24": [{"date": "2025-06-24", "time": "06:29:38", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-06-16": [{"date": "2025-06-16", "time": "05:44:38", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}], "2025-06-12": [{"date": "2025-06-12", "time": "07:14:51", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-06-12", "time": "06:52:01", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-06-10": [{"date": "2025-06-10", "time": "06:55:29", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-06-10", "time": "06:28:18", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-06-10", "time": "06:16:03", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-06-10", "time": "05:29:31", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-06-10", "time": "04:07:07", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-06-10", "time": "01:29:52", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}], "2025-06-04": [{"date": "2025-06-04", "time": "06:49:25", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-05-22": [{"date": "2025-05-22", "time": "06:40:53", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-05-20": [{"date": "2025-05-20", "time": "03:41:41", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-05-20", "time": "03:28:23", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-05-20", "time": "00:55:04", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-05-19": [{"date": "2025-05-19", "time": "08:41:06", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-19", "time": "08:30:29", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-19", "time": "07:41:15", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-19", "time": "07:36:02", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-05-16": [{"date": "2025-05-16", "time": "03:12:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-05-16", "time": "03:10:06", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-05-13": [{"date": "2025-05-13", "time": "06:12:14", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-05-13", "time": "06:09:53", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-05-13", "time": "03:39:11", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-13", "time": "03:35:06", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-05-09": [{"date": "2025-05-09", "time": "07:15:27", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-09", "time": "07:12:52", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-09", "time": "07:00:11", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-05-06": [{"date": "2025-05-06", "time": "07:30:01", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-05-06", "time": "07:26:13", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-30": [{"date": "2025-04-30", "time": "01:35:52", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-30", "time": "01:35:31", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}], "2025-04-29": [{"date": "2025-04-29", "time": "09:13:15", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "09:12:50", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "09:08:27", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "09:08:06", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "08:59:39", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "08:06:45", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "08:05:57", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "07:59:27", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "07:50:17", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "06:46:34", "action": "pushed to", "description": "pushed to in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}, {"date": "2025-04-29", "time": "05:26:40", "action": "pushed new", "description": "pushed new in chid-stoken", "project": "chid-stoken", "projectPath": "chid/chid-stoken"}], "2025-04-25": [{"date": "2025-04-25", "time": "02:33:20", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-24": [{"date": "2025-04-24", "time": "09:15:13", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-24", "time": "08:26:44", "action": "pushed to", "description": "pushed to in nlicense-sdk", "project": "nlicense-sdk", "projectPath": "nlicense/nlicense-sdk"}, {"date": "2025-04-24", "time": "01:48:43", "action": "pushed to", "description": "pushed to in nlicense-deployment-file", "project": "nlicense-deployment-file", "projectPath": "nlicense/nlicense-deployment-file"}], "2025-04-23": [{"date": "2025-04-23", "time": "07:38:13", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-23", "time": "07:37:16", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-21": [{"date": "2025-04-21", "time": "03:21:12", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-04-14": [{"date": "2025-04-14", "time": "03:45:18", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-14", "time": "03:26:20", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-11": [{"date": "2025-04-11", "time": "09:02:54", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-11", "time": "03:37:22", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-11", "time": "03:16:05", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-11", "time": "02:33:18", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-10": [{"date": "2025-04-10", "time": "09:47:38", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-10", "time": "04:07:07", "action": "accepted", "description": "accepted in carina-portal: Pre master", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-04-10", "time": "04:07:07", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-04-10", "time": "04:07:02", "action": "approved", "description": "approved in carina-portal: Pre master", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-04-10", "time": "04:06:59", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-04-10", "time": "04:06:59", "action": "accepted", "description": "accepted in carina-rest: Premaster", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-04-10", "time": "04:06:55", "action": "approved", "description": "approved in carina-rest: Premaster", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-04-10", "time": "03:37:13", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-10", "time": "02:18:26", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-10", "time": "01:30:30", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-09": [{"date": "2025-04-09", "time": "02:47:53", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-09", "time": "01:34:39", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-09", "time": "01:00:41", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-08": [{"date": "2025-04-08", "time": "06:15:46", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-08", "time": "06:11:15", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-08", "time": "01:56:06", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-08", "time": "01:16:25", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-08", "time": "01:08:21", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-08", "time": "00:55:29", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-07": [{"date": "2025-04-07", "time": "09:13:39", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "09:08:11", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "08:42:49", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "08:35:50", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "08:15:55", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "08:10:42", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "08:04:20", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "05:02:42", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "04:57:55", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "04:50:43", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "03:58:49", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-07", "time": "03:50:47", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}], "2025-04-04": [{"date": "2025-04-04", "time": "03:51:40", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-04", "time": "03:05:37", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-04", "time": "02:54:06", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-04", "time": "02:33:56", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-04", "time": "02:11:53", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-04-04", "time": "02:09:47", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}], "2025-04-03": [{"date": "2025-04-03", "time": "03:34:34", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-03", "time": "02:19:08", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-03", "time": "02:00:06", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-03", "time": "01:52:20", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-04-03", "time": "01:43:49", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}], "2025-03-28": [{"date": "2025-03-28", "time": "03:41:45", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "03:23:40", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "03:17:59", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "03:16:00", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "03:08:17", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "02:54:11", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "02:42:53", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "02:25:31", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "01:54:57", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "01:28:31", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "01:22:13", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-28", "time": "01:16:38", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}], "2025-03-27": [{"date": "2025-03-27", "time": "09:57:24", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "09:46:41", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "09:01:40", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "08:52:18", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "07:36:19", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "07:27:49", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "07:20:19", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "07:17:38", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "06:58:47", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "06:49:06", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "06:40:16", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "06:26:10", "action": "pushed to", "description": "pushed to in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-27", "time": "03:43:48", "action": "accepted", "description": "accepted in carina-rest: Premaster", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-03-27", "time": "03:43:48", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-03-27", "time": "03:43:21", "action": "approved", "description": "approved in carina-rest: Premaster", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-03-26": [{"date": "2025-03-26", "time": "06:11:15", "action": "pushed to", "description": "pushed to in nlicense-sdk", "project": "nlicense-sdk", "projectPath": "nlicense/nlicense-sdk"}, {"date": "2025-03-26", "time": "05:56:47", "action": "pushed to", "description": "pushed to in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-03-26", "time": "03:26:50", "action": "pushed new", "description": "pushed new in nlicense-deployment-file", "project": "nlicense-deployment-file", "projectPath": "nlicense/nlicense-deployment-file"}, {"date": "2025-03-26", "time": "03:23:18", "action": "pushed new", "description": "pushed new in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}, {"date": "2025-03-26", "time": "03:07:57", "action": "created", "description": "Created item in nlicense-admin-portal", "project": "nlicense-admin-portal", "projectPath": "nlicense/nlicense-admin-portal"}], "2025-03-25": [{"date": "2025-03-25", "time": "01:37:29", "action": "joined", "description": "joined in admin-portal-base", "project": "admin-portal-base", "projectPath": "ng-core/admin-portal-base"}], "2025-03-17": [{"date": "2025-03-17", "time": "05:29:23", "action": "joined", "description": "joined in juno-doc", "project": "juno-doc", "projectPath": "juno-doc/juno-doc"}], "2025-03-10": [{"date": "2025-03-10", "time": "09:04:59", "action": "pushed to", "description": "pushed to in chid-token", "project": "chid-token", "projectPath": "kai/chid-token"}, {"date": "2025-03-10", "time": "06:22:28", "action": "joined", "description": "joined in nlicense-sdk", "project": "nlicense-sdk", "projectPath": "nlicense/nlicense-sdk"}, {"date": "2025-03-10", "time": "06:22:16", "action": "joined", "description": "joined in nlicense-core", "project": "nlicense-core", "projectPath": "nlicense/nlicense-core"}, {"date": "2025-03-10", "time": "06:22:02", "action": "joined", "description": "joined in nlicense-deployment-file", "project": "nlicense-deployment-file", "projectPath": "nlicense/nlicense-deployment-file"}], "2025-03-03": [{"date": "2025-03-03", "time": "03:21:44", "action": "pushed to", "description": "pushed to in chid-token", "project": "chid-token", "projectPath": "kai/chid-token"}], "2025-02-28": [{"date": "2025-02-28", "time": "03:02:07", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-02-21": [{"date": "2025-02-21", "time": "07:35:05", "action": "pushed to", "description": "pushed to in chid-token", "project": "chid-token", "projectPath": "kai/chid-token"}], "2025-02-20": [{"date": "2025-02-20", "time": "08:20:02", "action": "pushed to", "description": "pushed to in chid-token", "project": "chid-token", "projectPath": "kai/chid-token"}], "2025-02-18": [{"date": "2025-02-18", "time": "02:58:41", "action": "pushed new", "description": "pushed new in chid-token", "project": "chid-token", "projectPath": "kai/chid-token"}], "2025-02-13": [{"date": "2025-02-13", "time": "04:12:17", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-02-13", "time": "04:12:17", "action": "accepted", "description": "accepted in carina-portal: Pre master", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2025-02-13", "time": "03:57:50", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-02-13", "time": "03:57:47", "action": "accepted", "description": "accepted in carina-rest: Premaster", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-01-24": [{"date": "2025-01-24", "time": "02:05:05", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-24", "time": "01:38:33", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-24", "time": "01:31:28", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-01-21": [{"date": "2025-01-21", "time": "01:48:41", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-21", "time": "01:48:21", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-21", "time": "01:26:29", "action": "joined", "description": "joined in chid-token", "project": "chid-token", "projectPath": "kai/chid-token"}], "2025-01-20": [{"date": "2025-01-20", "time": "08:50:31", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-20", "time": "08:50:07", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-20", "time": "07:11:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2025-01-13": [{"date": "2025-01-13", "time": "09:22:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-13", "time": "09:16:41", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-13", "time": "08:32:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2025-01-13", "time": "08:11:01", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-12-16": [{"date": "2024-12-16", "time": "00:51:50", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-12-06": [{"date": "2024-12-06", "time": "03:07:33", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-06", "time": "02:49:47", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-06", "time": "02:10:25", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-12-05": [{"date": "2024-12-05", "time": "08:32:37", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-05", "time": "08:10:36", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-12-04": [{"date": "2024-12-04", "time": "08:37:24", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "08:36:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "07:44:56", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "07:43:21", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "07:43:06", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "06:17:29", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "06:17:14", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "05:39:12", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "05:39:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-04", "time": "03:26:56", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-12-03": [{"date": "2024-12-03", "time": "07:42:29", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-03", "time": "07:42:07", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-03", "time": "06:58:22", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-03", "time": "06:56:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-12-03", "time": "06:00:38", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-29": [{"date": "2024-11-29", "time": "03:55:06", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-19": [{"date": "2024-11-19", "time": "02:50:22", "action": "accepted", "description": "accepted in carina-rest: 2.5.1", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-19", "time": "02:50:22", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-18": [{"date": "2024-11-18", "time": "05:16:40", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-18", "time": "04:06:22", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-18", "time": "04:06:05", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-18", "time": "03:59:51", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-12": [{"date": "2024-11-12", "time": "02:42:08", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-12", "time": "02:41:38", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-08": [{"date": "2024-11-08", "time": "02:47:16", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-07": [{"date": "2024-11-07", "time": "02:50:25", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-05": [{"date": "2024-11-05", "time": "06:02:47", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-11-04": [{"date": "2024-11-04", "time": "07:10:05", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-04", "time": "07:09:01", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-04", "time": "07:08:12", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-11-04", "time": "05:52:12", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-10-24": [{"date": "2024-10-24", "time": "09:04:25", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-24", "time": "08:46:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-24", "time": "08:11:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-24", "time": "07:58:52", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-24", "time": "07:58:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-10-22": [{"date": "2024-10-22", "time": "16:04:50", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-22", "time": "16:03:56", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-22", "time": "16:02:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-22", "time": "15:21:28", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-22", "time": "15:20:37", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-22", "time": "15:20:11", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-22", "time": "14:40:10", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-10-22", "time": "14:40:08", "action": "accepted", "description": "accepted in carina-portal: e-statement, paper statement, profile picture, update cardholder email", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-10-22", "time": "14:39:38", "action": "approved", "description": "approved in carina-portal: e-statement, paper statement, profile picture, update cardholder email", "project": "carina-portal", "projectPath": "carina/carina-portal"}], "2024-10-09": [{"date": "2024-10-09", "time": "09:10:06", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-09", "time": "09:09:49", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-09", "time": "05:37:17", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-09", "time": "05:36:17", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-10-07": [{"date": "2024-10-07", "time": "09:34:36", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "09:34:15", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "07:43:41", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "07:42:33", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "07:29:23", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "07:29:10", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "07:15:47", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "07:15:32", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "06:33:39", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "06:33:13", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "06:13:15", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "06:12:22", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "06:02:47", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "06:02:27", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "05:50:47", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "05:50:24", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "05:28:52", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "05:28:46", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "05:27:20", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "04:18:13", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-10-07", "time": "04:17:43", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-10-01": [{"date": "2024-10-01", "time": "03:44:03", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-09-20": [{"date": "2024-09-20", "time": "07:20:25", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "07:19:58", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "06:22:46", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "06:22:27", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "05:56:04", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "05:55:37", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "04:00:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "04:00:08", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "03:34:14", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "03:33:49", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "02:50:04", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "02:49:50", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "02:43:35", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "02:43:14", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "02:23:08", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-20", "time": "02:22:32", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-09-19": [{"date": "2024-09-19", "time": "01:32:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-09-18": [{"date": "2024-09-18", "time": "06:38:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-18", "time": "03:59:42", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-09-09": [{"date": "2024-09-09", "time": "03:25:17", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-09-03": [{"date": "2024-09-03", "time": "07:57:12", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-09-03", "time": "07:55:37", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-26": [{"date": "2024-08-26", "time": "23:52:09", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-26", "time": "23:49:37", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-26", "time": "07:27:31", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-26", "time": "07:25:08", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-23": [{"date": "2024-08-23", "time": "08:27:49", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-21": [{"date": "2024-08-21", "time": "03:28:39", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-20": [{"date": "2024-08-20", "time": "07:46:54", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-20", "time": "05:25:21", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-20", "time": "05:24:34", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-19": [{"date": "2024-08-19", "time": "03:15:19", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-19", "time": "03:13:36", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-19", "time": "03:13:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-19", "time": "03:12:56", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-19", "time": "00:59:06", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-19", "time": "00:54:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-14": [{"date": "2024-08-14", "time": "14:22:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-14", "time": "14:22:51", "action": "accepted", "description": "accepted in carina-rest: 2.3.0", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-14", "time": "14:00:32", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-08-14", "time": "14:00:31", "action": "accepted", "description": "accepted in carina-portal: 2.3.0", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-08-14", "time": "13:44:59", "action": "pushed new", "description": "pushed new in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-08-14", "time": "07:52:58", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-14", "time": "07:20:35", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-09": [{"date": "2024-08-09", "time": "06:36:57", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-08": [{"date": "2024-08-08", "time": "04:13:16", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-08", "time": "04:08:51", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-07": [{"date": "2024-08-07", "time": "05:54:39", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-06": [{"date": "2024-08-06", "time": "07:49:13", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "07:29:49", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "07:26:51", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "07:26:29", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "07:09:13", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "07:08:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "06:36:43", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "06:33:43", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "06:33:26", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "06:09:06", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "04:33:23", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "02:06:55", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "02:06:38", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "01:52:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "01:52:02", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "01:44:13", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "01:44:05", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "01:26:57", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-06", "time": "01:26:21", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-08-05": [{"date": "2024-08-05", "time": "08:55:37", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-05", "time": "08:54:48", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-08-05", "time": "06:41:04", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-07-31": [{"date": "2024-07-31", "time": "01:39:42", "action": "pushed new", "description": "pushed new in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-31", "time": "01:38:27", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-31", "time": "01:37:39", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-07-25": [{"date": "2024-07-25", "time": "02:47:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-25", "time": "02:43:52", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-25", "time": "01:08:14", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-07-24": [{"date": "2024-07-24", "time": "10:15:09", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "10:14:57", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "09:05:17", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "09:05:05", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "08:30:23", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "08:29:59", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "05:58:08", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "05:57:51", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "05:42:58", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "05:42:19", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "05:34:53", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "05:34:34", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "03:25:59", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "03:25:38", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "02:32:58", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "02:32:43", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "02:18:45", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "02:18:35", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "02:03:44", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "02:03:28", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "01:47:26", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-24", "time": "01:47:10", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-07-18": [{"date": "2024-07-18", "time": "10:58:43", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:57:52", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:57:08", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:55:31", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:54:29", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:46:21", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:42:49", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:38:17", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:37:15", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:26:39", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:19:39", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-18", "time": "10:17:32", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}], "2024-07-11": [{"date": "2024-07-11", "time": "07:08:20", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}], "2024-07-04": [{"date": "2024-07-04", "time": "08:32:00", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "08:32:00", "action": "accepted", "description": "accepted in carina-rest: <PERSON><PERSON> 2.2.0 to master", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "08:31:57", "action": "approved", "description": "approved in carina-rest: Merge 2.2.0 to master", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "07:58:17", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "07:58:14", "action": "accepted", "description": "accepted in carina-rest: <PERSON><PERSON> 2.2.0 to master", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "07:57:49", "action": "approved", "description": "approved in carina-rest: Merge 2.2.0 to master", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "05:49:30", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "05:48:42", "action": "pushed to", "description": "pushed to in carina-rest", "project": "carina-rest", "projectPath": "carina/carina-rest"}, {"date": "2024-07-04", "time": "02:54:36", "action": "accepted", "description": "accepted in carina-portal: Merge 2.2.0 to master", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-04", "time": "02:54:35", "action": "pushed to", "description": "pushed to in carina-portal", "project": "carina-portal", "projectPath": "carina/carina-portal"}, {"date": "2024-07-04", "time": "02:54:06", "action": "approved", "description": "approved in carina-portal: Merge 2.2.0 to master", "project": "carina-portal", "projectPath": "carina/carina-portal"}]}