这是一份开发手册 请你务必强制跟谁
如果有不确定 请你发问 而不是自己做决定
所有的代码和注释 都是英文的 请你要规范
在写任何代码之前 请你参考其他类似的代码怎么写
而不是自己做决定 如果没有可以参考的 请你问我 我会给你代码参考

---

### 关于架构

基本上每个项目都会分为两个 module，一个是*-endpoint-core，一个是*-mongodb-core
所有的数据库和 model 相关的都在 mongodb core
所有的 controller 和 service 都在 endpoint-core

---

### 关于\*-endpoint-core

#### 关于 controller

我们的项目，很多都会给两个前端 consume
一个是 app
一个是 admin portal
举个例子，假如你有两个模块，一个是 loyalty，一个是 voucher
你可能会有四个 controller，每个 class 分别叫
LoyaltyAppController
LoyaltyPortalController
VoucherAppController
VoucherPortalController
用 App 和 Portal 区分
这几个都放在 controller 文件夹下面

#### 关于 controller 的 request 和 response

在 \*-endpoint-core 的 module 下面的 src
下面有个 controller 的文件夹
就在这里面写 controller 的代码
还是一样 请你查看其他 controller 怎么写
controller 下面有两个文件夹
一个叫 model 一个叫 domian
domain：里面装 info，比如 License model 我们不会直接返回 license，而是包装多一层，只返回必要的信息， 命名规则叫 xxxxxInfo 比如 LicenseInfo，如果返回值是 list of License，需要用 xxxInfo 里面的 from 来把 xxx model 转换成 xxxxInfo
list of xxxxModel stream 转换之后变成 list of xxxInfo
例子
public class UserInfo {
private String id;
private String name;

    public static LicenseInfo from(User user) {
        return LicenseInfo.Builder.aLicenseInfo()
                .id(license.getId())
                .name(license.getName())
    }

model：里面装 request 和 response 的 model，命名规则是 xxxxRequest，xxxxResponse

而 controller 直接放在 controller 文件夹之下，比如 xxxxPortalController xxxxxAppController
写 controller 之前看别的案例 确保格式一模一样
@Tag 是给 swagger 的
@RestController 要加
@RequestMapping("/xxxxx") 比如 license 就是@RequestMapping("/license") user 就是 @RequestMapping("/user")
需要 import security 文件 例子@Import(UserControllerSecurityConfig.class)
如果没有专属的 security 文件 需要创建 怎么创建还是一样参考其他的

#### 关于 service

service 文件夹下面的结构是 impl 文件夹 model 文件夹 和 interface 直接在 service 文件夹下面
这个不用我多说妳肯定知道 model 放 service 的 request 和 response
impl 文件夹里面放的是 service 的实现类
interface 文件夹里面放的是 service 的接口

#### 关于 builder

我有个习惯，在数据库的 model，service 的 model，和 controller 的 model 包含各种 request response 我都喜欢用 builder
不要用 new，而是用 builder

#### 关于 manager 文件夹

一些特定的功能，比如初始化数据库的一些数据，或者定时任务，可以在 manager 文件夹里面写

#### 关于 security

security 文件夹下面的有些类是给 controller 用的 也有其他的 security 配置 主要是 controller
里面的内容是 security 的配置
如果有新的 controller 需要 security 的话
就新建一个 xxxxControllerSecurityConfig
里面的内容参考其他的

####

---

### 关于数据库和 Model

如果要用到的 model 或者 repo 数据库还没有创建
需要去到\*-data-mongodb-core 这个 module 下面去新建新的 module，流程如下
在你写任何代码之前 我要你参考其他的代码怎么写
如果没有案例 不要写 先问我

1. 去到 model 文件夹下面，新建一个 model
   如果 model 中需要用到 enum，在 model 下面的 type 写

2. 去到 repository 文件夹下面，新建 repo 文件
   使用的是 mongodb
   这一步，你要参考其他 repo 文件怎么写的
   我要你记着，可以写 findOneByXXXX findAllbyXXXXX 这种 springdata 的语法 永远优先写
   如果你需要比较复杂的 function 来写，你就需要 custom interface，然后开一个 impl file 去实现
   给你一个例子
   public interface LicenseRepository extends ListCrudRepository<License, String>, PagingAndSortingRepository<License, String>, LicenseRepositoryCustom {｝
   public interface LicenseRepositoryCustom {｝
   public class LicenseRepositoryImpl implements LicenseRepositoryCustom {｝
   LicenseRepository 专门写最基本的 比如 findOneByCode findAllByStatusIsIn
   而 custom 和 impl 文件就写复杂的
   记着 pagination 的这种 几乎都是 custom impl 的
   这个也有例子 如果没有 请你问我 而不是自己写

3. 注册好了之后，去到 repository 下面的 config 文件夹注册一个新的 config 文件
   参考其他文件，只修改 repo 名字和应用的 class 其他的设置保留

4. 去到 confif 文件下面 注意不是 repo 下面的 config 而是 config 下面找到\*CoreMongoAutoConfiguration
   在这个文件 找到 static class \*CoreMongoRepositoryConfiguration 的 component scan 里面填入新写的 repo 的 class
   参考其他的 model 的写法

---

### 实际开发步骤

现在我要开发一个新的 api，叫做 loyalty controller
首先我会检查\*-data-mongodb-core 里面是否已经有这个 api 会用到的 model 和 repo
如果有就直接用 如果没有就创建
如果有了 repo 但是要的方法没有 就直接写在 repo 里面 当然 repo 里面要用 springdata 的语法 findOneByXXX 这种
复杂的才用 Criteria query mongotemplate 这种

如果没有 model 先创建 model

---
