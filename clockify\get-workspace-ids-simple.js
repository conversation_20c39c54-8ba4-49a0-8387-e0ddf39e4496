const fs = require('fs');
const path = require('path');
const https = require('https');

// Read API Key
const apiKey = fs.readFileSync(path.join(__dirname, 'clockify-apikey.txt'), 'utf8').trim();

// Helper function to send HTTPS requests
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', reject);
        req.end();
    });
}

// Function to get projects
async function getProjects(workspaceId) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: `/api/v1/workspaces/${workspaceId}/projects`,
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        return await makeRequest(options);
    } catch (error) {
        console.error(`Failed to get projects for workspace ${workspaceId}:`, error.message);
        return [];
    }
}

// Main function
async function main() {
    const workspaceOptions = {
        hostname: 'api.clockify.me',
        port: 443,
        path: '/api/v1/workspaces',
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        const workspaces = await makeRequest(workspaceOptions);

        console.log('Workspaces and Projects:');
        console.log('========================');

        for (const workspace of workspaces) {
            console.log(`\nWorkspace: ${workspace.name}`);
            console.log(`Workspace ID: ${workspace.id}`);

            const projects = await getProjects(workspace.id);

            if (projects.length > 0) {
                console.log('Projects:');
                projects.forEach(project => {
                    console.log(`  - ${project.name} (ID: ${project.id})`);
                });
            } else {
                console.log('  No projects');
            }
        }

    } catch (error) {
        console.error('Request failed:', error.message);
    }
}

// Run main function
main();
