const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * Read Clockify API Key from file
 */
function readClockifyApiKey() {
    try {
        const apiKeyPath = path.join(__dirname, 'clockify-apikey.txt');
        const apiKey = fs.readFileSync(apiKeyPath, 'utf8').trim();
        if (!apiKey) {
            throw new Error('Clockify API Key is empty');
        }
        return apiKey;
    } catch (error) {
        console.error('Failed to read Clockify API Key:', error.message);
        process.exit(1);
    }
}

/**
 * Send HTTPS request
 */
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    reject(new Error('Failed to parse JSON response: ' + error.message));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error('Request failed: ' + error.message));
        });

        req.end();
    });
}

/**
 * Load configuration files
 */
function loadConfigFiles() {
    try {
        const holidays = JSON.parse(fs.readFileSync(path.join(__dirname, 'selangor-holiday.json'), 'utf8'));
        const ignoreDates = JSON.parse(fs.readFileSync(path.join(__dirname, 'date-to-be-ignore.json'), 'utf8'));
        const gitlabActivities = JSON.parse(fs.readFileSync(path.join(__dirname, 'clockify-grouped-by-date.json'), 'utf8'));
        const workspacesProjects = JSON.parse(fs.readFileSync(path.join(__dirname, 'workspaces-and-projects.json'), 'utf8'));

        return { holidays, ignoreDates, gitlabActivities, workspacesProjects };
    } catch (error) {
        console.error('Failed to load configuration files:', error.message);
        process.exit(1);
    }
}

/**
 * Get all holiday dates as array
 */
function getHolidayDates(holidays) {
    const holidayDates = [];

    // Extract 2024 and 2025 holidays
    if (holidays.Selangor && holidays.Selangor['2024']) {
        holidays.Selangor['2024'].public_holidays.forEach(holiday => {
            holidayDates.push(holiday.date);
        });
    }

    if (holidays.Selangor && holidays.Selangor['2025']) {
        holidays.Selangor['2025'].public_holidays.forEach(holiday => {
            holidayDates.push(holiday.date);
        });
    }

    return holidayDates;
}

/**
 * Check if date should be ignored (weekend, holiday, or specific ignore date)
 */
function shouldIgnoreDate(dateStr, holidayDates, ignoreDates) {
    // Use UTC date to avoid timezone issues
    const date = new Date(dateStr + 'T12:00:00.000Z'); // Add noon UTC to avoid timezone shifts
    const dayOfWeek = date.getUTCDay(); // 0 = Sunday, 6 = Saturday

    console.log(`Checking date ${dateStr}: day of week = ${dayOfWeek} (${['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]})`);

    // Skip weekends
    if (dayOfWeek === 0 || dayOfWeek === 6) {
        console.log(`  -> Skipping ${dateStr} (weekend)`);
        return true;
    }

    // Skip holidays
    if (holidayDates.includes(dateStr)) {
        console.log(`  -> Skipping ${dateStr} (holiday)`);
        return true;
    }

    // Skip specific ignore dates
    if (ignoreDates.date_to_be_ignore.includes(dateStr)) {
        console.log(`  -> Skipping ${dateStr} (ignore list)`);
        return true;
    }

    console.log(`  -> Including ${dateStr} (working day)`);
    return false;
}

/**
 * Generate date range from July 1, 2024 to today
 */
function generateDateRange() {
    const startDate = new Date('2024-07-01');
    const endDate = new Date();
    const dates = [];

    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
        dates.push(currentDate.toISOString().split('T')[0]);
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
}

/**
 * Map GitLab project to Clockify project
 */
function mapGitLabToClockifyProject(gitlabProject, workspacesProjects) {
    const workspace = workspacesProjects[0]; // Assuming single workspace

    if (!gitlabProject) {
        return null;
    }

    const projectLower = gitlabProject.toLowerCase();

    // Carina related projects
    if (projectLower.includes('carina')) {
        return workspace.projects.find(p => p.name === 'CB Bank (CB Card+)');
    }

    // nLicense related projects
    if (projectLower.includes('nlicense')) {
        return workspace.projects.find(p => p.name === 'nLicensing');
    }

    // Default fallback
    return workspace.projects.find(p => p.name === 'Internal Activity');
}

/**
 * Generate task description based on GitLab activity
 */
function generateTaskDescription(gitlabActivity, clockifyProject) {
    const projectName = clockifyProject?.name || 'Unknown Project';

    // Carina related
    if (projectName === 'CB Bank (CB Card+)') {
        return 'Carina Rest development/support/meeting';
    }

    // nLicense related
    if (projectName === 'nLicensing') {
        return 'nlicense development/support/meeting';
    }

    // Other projects
    if (projectName === 'CHID Mobile Soft Token') {
        return 'CHID Mobile Soft Token development/support/meeting';
    }

    if (projectName === 'Hammer Project') {
        return 'Hammer Project development/support/meeting';
    }

    if (projectName === 'Internal Activity') {
        return 'Internal Activity development/support/meeting';
    }

    // Default fallback
    return `${projectName} development/support/meeting`;
}

/**
 * Get existing Clockify time entries for date range
 */
async function getExistingTimeEntries(apiKey, workspaceId, userId, startDate, endDate) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: `/api/v1/workspaces/${workspaceId}/user/${userId}/time-entries?start=${startDate}T00:00:00.000Z&end=${endDate}T23:59:59.999Z&page-size=1000`,
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        console.log('Fetching existing Clockify time entries...');
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            const existingDates = new Set();
            response.data.forEach(entry => {
                const date = entry.timeInterval.start.split('T')[0];
                existingDates.add(date);
            });
            return existingDates;
        } else {
            console.error('Failed to get existing time entries:', response.statusCode);
            return new Set();
        }
    } catch (error) {
        console.error('Error fetching existing time entries:', error.message);
        return new Set();
    }
}

/**
 * Get current user info
 */
async function getCurrentUser(apiKey) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: '/api/v1/user',
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);
        if (response.statusCode === 200) {
            return response.data;
        }
        return null;
    } catch (error) {
        console.error('Error getting current user:', error.message);
        return null;
    }
}

/**
 * Generate work time (8:55-9:00 AM to 6:00-6:05 PM) in Malaysia timezone
 * Returns UTC time for Clockify API
 */
function generateWorkTime(date) {
    // Start time: 8:55-9:00 AM Malaysia time
    const startMinutes = Math.floor(Math.random() * 6) + 55; // 55-60 minutes (but 60 becomes 0 of next hour)
    const startHour = startMinutes === 60 ? 9 : 8;
    const actualStartMinutes = startMinutes === 60 ? 0 : startMinutes;

    // End time: 6:00-6:05 PM Malaysia time
    const endMinutes = Math.floor(Math.random() * 6); // 0-5 minutes
    const endHour = 18; // 6 PM

    // Create proper Date objects for Malaysia timezone
    const startDateMalaysia = new Date(`${date}T${startHour.toString().padStart(2, '0')}:${actualStartMinutes.toString().padStart(2, '0')}:00+08:00`);
    const endDateMalaysia = new Date(`${date}T${endHour.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}:00+08:00`);

    // Convert to UTC ISO strings
    const startUTC = startDateMalaysia.toISOString();
    const endUTC = endDateMalaysia.toISOString();

    console.log(`Work time for ${date}:`);
    console.log(`  Malaysia: ${startHour.toString().padStart(2, '0')}:${actualStartMinutes.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`);
    console.log(`  UTC: ${startUTC} - ${endUTC}`);

    return {
        start: startUTC,
        end: endUTC,
        startMalaysia: `${startHour.toString().padStart(2, '0')}:${actualStartMinutes.toString().padStart(2, '0')}:00`,
        endMalaysia: `${endHour.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}:00`
    };
}

/**
 * Main function
 */
async function main() {
    console.log('Clockify Time Entry Generator');
    console.log('='.repeat(50));

    try {
        const apiKey = readClockifyApiKey();
        const { holidays, ignoreDates, gitlabActivities, workspacesProjects } = loadConfigFiles();

        // Get current user and workspace
        const user = await getCurrentUser(apiKey);
        if (!user) {
            console.error('Failed to get user information');
            process.exit(1);
        }

        const workspaceId = workspacesProjects[0].workspace.id;
        console.log(`User: ${user.name} (@${user.email})`);
        console.log(`Workspace: ${workspacesProjects[0].workspace.name}`);

        // Generate date range and filter
        const allDates = generateDateRange();
        const holidayDates = getHolidayDates(holidays);

        console.log(`\nTotal dates in range: ${allDates.length}`);

        // Get existing time entries
        const existingEntries = await getExistingTimeEntries(
            apiKey,
            workspaceId,
            user.id,
            '2024-07-01',
            new Date().toISOString().split('T')[0]
        );

        console.log(`Existing time entries found: ${existingEntries.size}`);

        // Filter dates that need time entries
        const datesToProcess = allDates.filter(date => {
            if (shouldIgnoreDate(date, holidayDates, ignoreDates)) {
                return false;
            }
            if (existingEntries.has(date)) {
                return false;
            }
            return true;
        });

        console.log(`Dates needing time entries: ${datesToProcess.length}`);

        // Generate time entries
        const timeEntries = [];
        let lastProject = null;

        datesToProcess.forEach(date => {
            const gitlabActivity = gitlabActivities[date];
            let clockifyProject;

            if (gitlabActivity && gitlabActivity.length > 0) {
                // Use GitLab activity project
                clockifyProject = mapGitLabToClockifyProject(gitlabActivity[0].project, workspacesProjects);
                lastProject = clockifyProject;
            } else {
                // Use previous day's project or default
                clockifyProject = lastProject || workspacesProjects[0].projects.find(p => p.name === 'Internal Activity');
            }

            const workTime = generateWorkTime(date);
            const description = generateTaskDescription(gitlabActivity?.[0], clockifyProject);

            timeEntries.push({
                date: date,
                projectId: clockifyProject.id,
                projectName: clockifyProject.name,
                description: description,
                start: workTime.start,
                end: workTime.end,
                workspaceId: workspaceId,
                userId: user.id,
                // Debug info
                malaysiaTime: {
                    start: `${date} ${workTime.startMalaysia}`,
                    end: `${date} ${workTime.endMalaysia}`
                }
            });
        });

        console.log(`\nGenerated ${timeEntries.length} time entries`);

        // Save to file
        const outputPath = path.join(__dirname, 'clockify-entries-to-import.json');
        fs.writeFileSync(outputPath, JSON.stringify(timeEntries, null, 2));
        console.log(`Time entries saved to: ${outputPath}`);

        // Show summary
        console.log('\nSummary:');
        console.log(`- Total dates in range: ${allDates.length}`);
        console.log(`- Weekends/holidays/ignored: ${allDates.length - datesToProcess.length - existingEntries.size}`);
        console.log(`- Already have entries: ${existingEntries.size}`);
        console.log(`- New entries to create: ${timeEntries.length}`);

    } catch (error) {
        console.error('Execution failed:', error.message);
        process.exit(1);
    }
}

// Run script
if (require.main === module) {
    main();
}

module.exports = {
    main,
    generateDateRange,
    mapGitLabToClockifyProject,
    generateTaskDescription
};
