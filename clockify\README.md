# Clockify 自动化时间记录工具

这是一套完整的 Clockify 时间记录自动化工具，可以从 GitLab 活动自动生成并导入时间记录到 Clockify。

## 📋 功能概述

- **GitLab 活动获取**: 自动获取 GitLab 项目活动记录
- **Clockify 项目管理**: 获取和管理 Clockify 工作区和项目
- **智能时间生成**: 根据 GitLab 活动自动生成工作时间记录
- **批量导入**: 批量导入时间记录到 Clockify
- **批量删除**: 安全删除已导入的时间记录

## 🛠️ 环境要求

- **Node.js**: 版本 14 或更高
- **Clockify 账户**: 有效的 Clockify 账户和 API Key
- **GitLab 访问**: GitLab 服务器访问权限和 API Token

## 📁 文件结构

### 🔧 脚本文件
- `get-gitlab-activities.js` - 获取 GitLab 活动记录
- `get-workspaces.js` - 获取 Clockify 工作区和项目信息
- `generate-clockify-entries.js` - 生成待导入的时间记录
- `import-to-clockify.js` - 批量导入时间记录到 Clockify
- `delete-clockify-entries.js` - 批量删除时间记录

### 📄 配置文件
- `clockify-apikey.txt` - Clockify API Key
- `gitlab-apikey.txt` - GitLab API Token
- `selangor-holiday.json` - 马来西亚雪兰莪州节假日
- `date-to-be-ignore.json` - 需要忽略的特定日期
- `workspaces-and-projects.json` - Clockify 工作区和项目信息

### 📊 数据文件
- `clockify-grouped-by-date.json` - 按日期分组的 GitLab 活动
- `clockify-entries-to-import.json` - 待导入的时间记录
- `clockify-import-report.json` - 导入结果报告
- `clockify-deletion-report.json` - 删除结果报告

## 🚀 使用步骤

### 步骤 1: 环境准备

1. **安装 Node.js**
   - 访问 https://nodejs.org/
   - 下载并安装 LTS 版本

2. **获取 API Keys**
   - **Clockify API Key**:
     - 登录 Clockify → 右上角头像 → Settings → API
     - 生成新的 API Key
     - 将 API Key 保存到 `clockify-apikey.txt` 文件
   
   - **GitLab API Token**:
     - 登录 GitLab → 右上角头像 → Settings → Access Tokens
     - 创建 Personal Access Token，权限选择 `read_api`, `read_user`
     - 将 Token 保存到 `gitlab-apikey.txt` 文件

3. **配置 GitLab 服务器地址**
   - 如果使用自建 GitLab，需要修改脚本中的服务器地址
   - 默认配置为: `http://192.168.88.76/`

### 步骤 2: 获取基础数据

1. **获取 Clockify 工作区信息**
   ```bash
   node get-workspaces.js
   ```
   - 生成 `workspaces-and-projects.json` 文件
   - 包含所有工作区和项目信息

2. **获取 GitLab 活动记录**
   ```bash
   node get-gitlab-activities.js
   ```
   - 生成 `clockify-grouped-by-date.json` 文件
   - 包含从 2024年7月1日到今天的 GitLab 活动

### 步骤 3: 配置忽略日期

1. **节假日配置**
   - `selangor-holiday.json` 已包含马来西亚雪兰莪州节假日
   - 可根据需要修改或添加其他地区节假日

2. **特定忽略日期**
   - 编辑 `date-to-be-ignore.json` 文件
   - 添加需要忽略的特定日期（如请假日期）
   - 格式: `"YYYY-MM-DD"`

### 步骤 4: 生成时间记录

```bash
node generate-clockify-entries.js
```

**功能说明:**
- 时间范围: 2024年7月1日到今天
- 自动跳过: 周末、节假日、特定忽略日期
- 工作时间: 8:55-9:00 AM 到 6:00-6:05 PM (马来西亚时间)
- 检查重复: 自动检查 Clockify 中已有的时间记录
- 项目映射: 
  - Carina 相关项目 → CB Bank (CB Card+)
  - nLicense 相关项目 → nLicensing
  - 其他项目 → Internal Activity

**生成文件:**
- `clockify-entries-to-import.json` - 待导入的时间记录

### 步骤 5: 导入时间记录

```bash
node import-to-clockify.js
```

**功能说明:**
- 读取 `clockify-entries-to-import.json` 文件
- 批量导入到 Clockify
- 10秒确认时间，可按 Ctrl+C 取消
- 自动处理 API 速率限制
- 实时显示导入进度

**生成文件:**
- `clockify-import-report.json` - 导入结果报告

### 步骤 6: 删除时间记录 (可选)

如果需要删除已导入的时间记录:

```bash
node delete-clockify-entries.js
```

**功能说明:**
- 只删除通过脚本导入的时间记录
- 10秒确认时间，可按 Ctrl+C 取消
- 安全匹配: 必须同时匹配日期和项目ID
- 不会删除手动创建的其他记录

**生成文件:**
- `clockify-deletion-report.json` - 删除结果报告

## ⚙️ 高级配置

### 项目映射规则

在 `generate-clockify-entries.js` 中的 `mapGitLabToClockifyProject` 函数:

```javascript
// Carina 相关项目
if (projectLower.includes('carina')) {
    return workspace.projects.find(p => p.name === 'CB Bank (CB Card+)');
}

// nLicense 相关项目  
if (projectLower.includes('nlicense')) {
    return workspace.projects.find(p => p.name === 'nLicensing');
}
```

### 任务描述规则

在 `generateTaskDescription` 函数中:
- Carina 项目: "Carina Rest development/support/meeting"
- nLicense 项目: "nlicense development/support/meeting"
- 其他项目: "[项目名] development/support/meeting"

### 时间范围修改

如需修改时间范围，在 `generateDateRange` 函数中修改:
```javascript
const startDate = new Date('2024-07-01'); // 修改开始日期
```

## 🔍 故障排除

### 常见问题

1. **API Key 错误**
   - 检查 `clockify-apikey.txt` 和 `gitlab-apikey.txt` 文件内容
   - 确保 API Key 有效且权限正确

2. **网络连接问题**
   - 检查 GitLab 服务器地址是否正确
   - 确保网络可以访问 Clockify API (api.clockify.me)

3. **时区问题**
   - 脚本默认使用马来西亚时区 (UTC+8)
   - 如需修改时区，需要调整 `generateWorkTime` 函数

4. **项目映射问题**
   - 检查 `workspaces-and-projects.json` 中的项目列表
   - 确保项目映射规则正确

### 日志查看

所有脚本都会输出详细的执行日志，包括:
- 处理的日期和项目信息
- API 调用结果
- 错误信息和警告

### 报告文件

- `clockify-import-report.json` - 查看导入成功/失败统计
- `clockify-deletion-report.json` - 查看删除操作结果

## 📞 技术支持

如遇到问题，请检查:
1. Node.js 版本是否符合要求
2. API Keys 是否有效
3. 网络连接是否正常
4. 配置文件格式是否正确

## 🔒 安全注意事项

- **API Keys 保密**: 不要将 API Key 文件提交到版本控制系统
- **备份数据**: 在批量操作前建议备份重要数据
- **测试环境**: 建议先在测试环境验证脚本功能
- **权限控制**: 确保 API Keys 只有必要的权限

## 📝 更新日志

- **v1.0**: 初始版本，支持基本的时间记录生成和导入
- **v1.1**: 修复时区问题，改进周末检查逻辑
- **v1.2**: 添加批量删除功能，改进错误处理

---

**注意**: 此工具专为马来西亚时区和特定的 GitLab/Clockify 环境设计，使用前请根据实际情况调整配置。
