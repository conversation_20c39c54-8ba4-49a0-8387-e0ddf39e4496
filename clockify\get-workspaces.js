const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * Read API Key from file
 */
function readApiKey() {
    try {
        const apiKeyPath = path.join(__dirname, 'clockify-apikey.txt');
        const apiKey = fs.readFileSync(apiKeyPath, 'utf8').trim();
        if (!apiKey) {
            throw new Error('API Key is empty');
        }
        return apiKey;
    } catch (error) {
        console.error('Failed to read API Key:', error.message);
        process.exit(1);
    }
}

/**
 * Send HTTPS request
 */
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        statusCode: res.statusCode,
                        data: jsonData
                    });
                } catch (error) {
                    reject(new Error('Failed to parse JSON response: ' + error.message));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error('Request failed: ' + error.message));
        });

        req.end();
    });
}

/**
 * Get projects for a specific workspace
 */
async function getProjects(workspaceId, apiKey) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: `/api/v1/workspaces/${workspaceId}/projects`,
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            return response.data;
        } else {
            console.error(`Failed to get projects for workspace ${workspaceId}, status code: ${response.statusCode}`);
            return [];
        }

    } catch (error) {
        console.error(`Failed to get projects for workspace ${workspaceId}:`, error.message);
        return [];
    }
}

/**
 * Get all workspaces and projects
 */
async function getWorkspacesAndProjects() {
    const apiKey = readApiKey();

    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: '/api/v1/workspaces',
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        console.log('Fetching workspaces...');
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            const workspaces = response.data;

            console.log(`\nFound ${workspaces.length} workspaces:\n`);

            const allData = [];

            for (let i = 0; i < workspaces.length; i++) {
                const workspace = workspaces[i];
                console.log(`${i + 1}. Workspace: ${workspace.name}`);
                console.log(`   Workspace ID: ${workspace.id}`);

                // Get projects for this workspace
                const projects = await getProjects(workspace.id, apiKey);

                if (projects.length > 0) {
                    console.log(`   Projects (${projects.length}):`);
                    projects.forEach((project, projectIndex) => {
                        console.log(`     ${projectIndex + 1}. ${project.name} (ID: ${project.id})`);
                    });
                } else {
                    console.log('   No projects found');
                }

                allData.push({
                    workspace: {
                        id: workspace.id,
                        name: workspace.name
                    },
                    projects: projects.map(p => ({
                        id: p.id,
                        name: p.name
                    }))
                });

                console.log('');
            }

            return allData;

        } else {
            console.error(`API request failed, status code: ${response.statusCode}`);
            console.error('Response:', response.data);
            process.exit(1);
        }

    } catch (error) {
        console.error('Failed to get workspaces:', error.message);
        process.exit(1);
    }
}

/**
 * Main function
 */
async function main() {
    console.log('Clockify Workspace and Project Fetcher');
    console.log('='.repeat(40));

    try {
        const allData = await getWorkspacesAndProjects();

        // Save workspace IDs to file
        const workspaceIds = allData.map(item => item.workspace.id);
        const idsFilePath = path.join(__dirname, 'workspace-ids.txt');
        fs.writeFileSync(idsFilePath, workspaceIds.join('\n'));
        console.log(`\nWorkspace IDs saved to: ${idsFilePath}`);

        // Save complete data to JSON file
        const dataFilePath = path.join(__dirname, 'workspaces-and-projects.json');
        fs.writeFileSync(dataFilePath, JSON.stringify(allData, null, 2));
        console.log(`Complete data saved to: ${dataFilePath}`);

    } catch (error) {
        console.error('Execution failed:', error.message);
        process.exit(1);
    }
}

// Run script
if (require.main === module) {
    main();
}

module.exports = {
    getWorkspacesAndProjects,
    getProjects,
    readApiKey
};
