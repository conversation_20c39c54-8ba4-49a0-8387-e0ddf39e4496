loyatly card app

controller:subscribers
path: /subscriber
api:
/profile

controller:loyalty
path: /loyalty
api:
/getAllLoyaltyCards
return all loyalty cards, and card infomation

/getCardDetail
return card detail (including points, card no, etc)

/requstForLoyaltyCard
user request loyalty card

/pointsHistory
check points history

/getMyVouchers
check all redeemed vouchers

/getAvailableVouchers
check all available vouchers (can be redeemed)

/redeemVoucher
redeem voucher with points

/voucherReedeemtionQr
return a text, mobile will make it a qr and show to merchant

loyatlty card admin portal
/getCardRequestList
/assignCard
/markAsAssigned
/readCard

sdk
/collectPoints
/redeemPoints
/callbackPoints
