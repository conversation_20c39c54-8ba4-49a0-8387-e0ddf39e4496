const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * Read Clockify API Key from file
 */
function readClockifyApiKey() {
    try {
        const apiKeyPath = path.join(__dirname, 'clockify-apikey.txt');
        const apiKey = fs.readFileSync(apiKeyPath, 'utf8').trim();
        if (!apiKey) {
            throw new Error('Clockify API Key is empty');
        }
        return apiKey;
    } catch (error) {
        console.error('Failed to read Clockify API Key:', error.message);
        process.exit(1);
    }
}

/**
 * Send HTTPS request
 */
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = data ? JSON.parse(data) : {};
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error('Request failed: ' + error.message));
        });

        if (postData) {
            req.write(postData);
        }

        req.end();
    });
}

/**
 * Get current user info
 */
async function getCurrentUser(apiKey) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: '/api/v1/user',
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);
        if (response.statusCode === 200) {
            return response.data;
        }
        return null;
    } catch (error) {
        console.error('Error getting current user:', error.message);
        return null;
    }
}

/**
 * Get time entries for a date range
 */
async function getTimeEntries(apiKey, workspaceId, userId, startDate, endDate) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: `/api/v1/workspaces/${workspaceId}/user/${userId}/time-entries?start=${startDate}T00:00:00.000Z&end=${endDate}T23:59:59.999Z&page-size=1000`,
        method: 'GET',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200) {
            return response.data;
        } else {
            console.error('Failed to get time entries:', response.statusCode);
            return [];
        }
    } catch (error) {
        console.error('Error fetching time entries:', error.message);
        return [];
    }
}

/**
 * Delete a single time entry
 */
async function deleteTimeEntry(apiKey, workspaceId, timeEntryId) {
    const options = {
        hostname: 'api.clockify.me',
        port: 443,
        path: `/api/v1/workspaces/${workspaceId}/time-entries/${timeEntryId}`,
        method: 'DELETE',
        headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 204) {
            return { success: true };
        } else {
            return {
                success: false,
                error: `HTTP ${response.statusCode}: ${JSON.stringify(response.data)}`
            };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * Add delay between requests to avoid rate limiting
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Load imported entries from file
 */
function loadImportedEntries() {
    try {
        const filePath = path.join(__dirname, 'clockify-entries-to-import.json');
        const entries = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        return entries;
    } catch (error) {
        console.error('Failed to load imported entries file:', error.message);
        return [];
    }
}

/**
 * Delete time entries based on imported data
 */
async function deleteImportedEntries(apiKey, workspaceId, userId) {
    console.log('Fetching time entries to delete...');

    // Get date range from imported entries
    const importedEntries = loadImportedEntries();
    if (importedEntries.length === 0) {
        console.log('No imported entries found to delete.');
        return { success: 0, failed: 0, errors: [] };
    }

    const dates = importedEntries.map(entry => entry.date);
    const startDate = Math.min(...dates.map(d => new Date(d)));
    const endDate = Math.max(...dates.map(d => new Date(d)));

    const startDateStr = new Date(startDate).toISOString().split('T')[0];
    const endDateStr = new Date(endDate).toISOString().split('T')[0];

    console.log(`Date range: ${startDateStr} to ${endDateStr}`);

    // Get all time entries in the date range
    const timeEntries = await getTimeEntries(apiKey, workspaceId, userId, startDateStr, endDateStr);

    console.log(`Found ${timeEntries.length} time entries in Clockify`);
    console.log(`Expected to delete ${importedEntries.length} entries`);

    if (timeEntries.length === 0) {
        console.log('No time entries found to delete.');
        return { success: 0, failed: 0, errors: [] };
    }

    // Filter entries that match our imported data (by date and project)
    const importedDates = new Set(importedEntries.map(e => e.date));
    const importedProjects = new Set(importedEntries.map(e => e.projectId));

    const entriesToDelete = timeEntries.filter(entry => {
        // Check if entry has valid timeInterval and start time
        if (!entry.timeInterval || !entry.timeInterval.start) {
            console.log('Warning: Entry missing timeInterval.start:', entry.id);
            return false;
        }

        try {
            const entryDate = entry.timeInterval.start.split('T')[0];
            const projectId = entry.projectId;
            return importedDates.has(entryDate) && importedProjects.has(projectId);
        } catch (error) {
            console.log('Warning: Error processing entry:', entry.id, error.message);
            return false;
        }
    });

    console.log(`Filtered to ${entriesToDelete.length} entries that match imported data`);

    if (entriesToDelete.length === 0) {
        console.log('No matching entries found to delete.');
        return { success: 0, failed: 0, errors: [] };
    }

    // Show preview
    console.log('\nEntries to delete:');
    console.log('-'.repeat(60));
    entriesToDelete.slice(0, 5).forEach((entry, index) => {
        try {
            const date = entry.timeInterval?.start?.split('T')[0] || 'Unknown date';
            const startTime = entry.timeInterval?.start?.split('T')[1]?.split('.')[0] || 'Unknown time';
            const endTime = entry.timeInterval?.end?.split('T')[1]?.split('.')[0] || 'Unknown time';
            const description = entry.description || 'No description';
            console.log(`${index + 1}. ${date} ${startTime}-${endTime} - ${description}`);
        } catch (error) {
            console.log(`${index + 1}. Entry ID: ${entry.id} - Error displaying details: ${error.message}`);
        }
    });

    if (entriesToDelete.length > 5) {
        console.log(`   ... and ${entriesToDelete.length - 5} more entries`);
    }

    // Confirm deletion
    console.log('\n⚠️  This will PERMANENTLY DELETE these time entries from Clockify.');
    console.log('Press Ctrl+C to cancel, or wait 10 seconds to continue...');

    await delay(10000);

    // Start deletion
    console.log('\nStarting deletion process...');
    console.log('='.repeat(60));

    const results = {
        success: 0,
        failed: 0,
        errors: []
    };

    for (let i = 0; i < entriesToDelete.length; i++) {
        const entry = entriesToDelete[i];
        const progress = `${i + 1}/${entriesToDelete.length}`;

        let date = 'Unknown date';
        let description = 'No description';

        try {
            date = entry.timeInterval?.start?.split('T')[0] || 'Unknown date';
            description = entry.description || 'No description';
        } catch (error) {
            console.log(`Warning: Error getting entry details for ${entry.id}: ${error.message}`);
        }

        console.log(`[${progress}] Deleting ${date} - ${description}...`);

        const result = await deleteTimeEntry(apiKey, workspaceId, entry.id);

        if (result.success) {
            results.success++;
            console.log(`  ✅ Deleted`);
        } else {
            results.failed++;
            results.errors.push({
                id: entry.id,
                date: date,
                description: description,
                error: result.error
            });
            console.log(`  ❌ Failed: ${result.error}`);
        }

        // Add delay to avoid rate limiting
        if (i < entriesToDelete.length - 1) {
            await delay(250);
        }
    }

    return results;
}

/**
 * Save deletion results to file
 */
function saveDeletionResults(results, totalEntries) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: totalEntries,
            successful: results.success,
            failed: results.failed,
            successRate: `${((results.success / totalEntries) * 100).toFixed(1)}%`
        },
        errors: results.errors
    };

    const reportPath = path.join(__dirname, 'clockify-deletion-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\nDeletion report saved to: ${reportPath}`);

    return report;
}

/**
 * Main function
 */
async function main() {
    console.log('Clockify Time Entry Deletion Tool');
    console.log('='.repeat(50));

    try {
        const apiKey = readClockifyApiKey();

        // Get current user
        const user = await getCurrentUser(apiKey);
        if (!user) {
            console.error('Failed to get user information');
            process.exit(1);
        }

        // Load workspace info
        const workspacesProjects = JSON.parse(fs.readFileSync(path.join(__dirname, 'workspaces-and-projects.json'), 'utf8'));
        const workspaceId = workspacesProjects[0].workspace.id;

        console.log(`User: ${user.name} (@${user.email})`);
        console.log(`Workspace: ${workspacesProjects[0].workspace.name}`);

        // Delete entries
        const results = await deleteImportedEntries(apiKey, workspaceId, user.id);

        // Show results
        console.log('\n' + '='.repeat(60));
        console.log('DELETION COMPLETED');
        console.log('='.repeat(60));
        console.log(`✅ Successful: ${results.success}`);
        console.log(`❌ Failed: ${results.failed}`);

        if (results.success + results.failed > 0) {
            console.log(`📊 Success Rate: ${((results.success / (results.success + results.failed)) * 100).toFixed(1)}%`);
        }

        if (results.errors.length > 0) {
            console.log('\nErrors:');
            results.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.date}: ${error.error}`);
            });
        }

        // Save report
        saveDeletionResults(results, results.success + results.failed);

        console.log('\n🎉 Deletion process completed!');

    } catch (error) {
        console.error('Deletion failed:', error.message);
        process.exit(1);
    }
}

// Run script
if (require.main === module) {
    main();
}

module.exports = {
    main,
    deleteTimeEntry,
    deleteImportedEntries
};
